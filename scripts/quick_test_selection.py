#!/usr/bin/env python3
"""
选股功能快速测试脚本
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.strategies.strategy_manager import StrategyManager


def quick_test_strategy(strategy_name, max_stocks=50):
    """快速测试策略执行"""
    print(f"快速测试策略: {strategy_name}")
    print("=" * 50)

    try:
        # 初始化数据访问
        config = db_config.get_mysql_config()
        data_access = MySQLDataAccess(**config)

        # 创建策略管理器
        strategy_manager = StrategyManager(data_access)

        # 创建策略实例
        strategy = strategy_manager.create_strategy(strategy_name)
        if not strategy:
            print(f"❌ 无法创建策略实例")
            return False

        print(f"✅ 策略实例创建成功")

        # 获取策略配置
        config_info = strategy.get_config()
        print(f"📋 策略配置: {config_info}")

        # 创建一个模拟的数据访问器，只返回少量股票
        class LimitedDataAccess:
            def __init__(self, original_data_access, max_stocks):
                self.original = original_data_access
                self.max_stocks = max_stocks

            def get_all_stock_codes(self):
                all_codes = self.original.get_all_stock_codes()
                # 只返回前N只股票进行测试
                return all_codes[:self.max_stocks]

            def __getattr__(self, name):
                # 其他方法直接委托给原始对象
                return getattr(self.original, name)

        limited_data_access = LimitedDataAccess(data_access, max_stocks)

        # 执行策略
        print(f"🔄 执行策略选股（限制{max_stocks}只股票）...")
        start_time = datetime.now()

        results = strategy.execute(limited_data_access)

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # 显示结果
        if results:
            print(f"✅ 选股成功，共选中 {len(results)} 只股票")
            print(f"⏱️  执行时间: {execution_time:.2f} 秒")
            print("\n选股结果:")
            print("-" * 60)
            for i, result in enumerate(results, 1):
                print(f"{i:2d}. {result['stock_code']} - {result.get('stock_name', '')}")
                print(f"    评分: {result['score']:.2f}")
                print(f"    原因: {result['reason']}")
                print()
        else:
            print("⚠️  未选中任何股票")
            print("💡 这可能是正常的，取决于当前市场条件和策略参数")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        try:
            data_access.close_connection()
        except:
            pass


def test_selection_app():
    """测试选股应用"""
    print("测试选股应用")
    print("=" * 50)

    try:
        from src.selection_app import SelectionApp

        app = SelectionApp()
        print("✅ 选股应用创建成功")

        # 初始化应用
        if not app.initialize('mysql'):
            print("❌ 选股应用初始化失败")
            return False

        print("✅ 选股应用初始化成功")

        # 测试获取可用策略（通过策略管理器）
        strategies = app.strategy_manager.get_available_strategies()
        print(f"📋 可用策略: {strategies}")

        # 测试策略信息
        if strategies:
            strategy_name = strategies[0]
            print(f"🔍 测试策略信息: {strategy_name}")
            app.show_strategy_info(strategy_name)

            print("💡 策略运行测试跳过（避免长时间等待）")
            print("💡 可以手动运行: python run_selection.py")

        # 清理资源
        app.cleanup()
        return True

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("A股选股系统 - 快速功能测试")
    print("=" * 80)

    # 1. 测试数据库连接
    print("测试数据库连接")
    print("=" * 50)

    try:
        config = db_config.get_mysql_config()
        data_access = MySQLDataAccess(**config)

        stock_codes = data_access.get_all_stock_codes()
        print(f"✅ 数据库连接成功")
        print(f"📊 股票数量: {len(stock_codes)}")

        data_access.close_connection()
        db_ok = True

    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        db_ok = False

    print()

    if not db_ok:
        print("❌ 数据库连接失败，无法继续测试")
        return 1

    # 2. 快速测试策略
    strategies_ok = True

    # 测试技术反转策略
    print("测试技术反转策略（快速模式）")
    print("-" * 50)
    tech_ok = quick_test_strategy('technical_reversal', max_stocks=20)
    print()

    # 测试成交量异常策略
    print("测试成交量异常策略（快速模式）")
    print("-" * 50)
    vol_ok = quick_test_strategy('volume_anomaly', max_stocks=20)
    print()

    strategies_ok = tech_ok and vol_ok

    # 3. 测试选股应用
    app_ok = test_selection_app()
    print()

    # 4. 总结
    print("测试总结:")
    print("=" * 50)
    print(f"数据库连接: {'✅ 正常' if db_ok else '❌ 失败'}")
    print(f"策略执行: {'✅ 正常' if strategies_ok else '❌ 失败'}")
    print(f"选股应用: {'✅ 正常' if app_ok else '❌ 失败'}")

    if db_ok and strategies_ok and app_ok:
        print("\n🎉 快速测试通过，系统基本功能正常！")
        print("\n可以开始使用选股功能:")
        print("python run_selection.py")
        print("\n或者运行完整测试:")
        print("python scripts/test_selection.py")
        return 0
    else:
        print("\n⚠️  存在问题，请检查配置和数据")
        return 1


if __name__ == "__main__":
    exit(main())
