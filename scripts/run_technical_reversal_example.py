#!/usr/bin/env python3
"""
技术反转策略使用示例
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.strategies.strategy_manager import StrategyManager

def run_technical_reversal_example():
    """运行技术反转策略示例"""
    print("技术反转策略使用示例")
    print("="*60)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建策略管理器
        strategy_manager = StrategyManager(data_access)
        
        print("1. 获取策略信息")
        print("-" * 30)
        strategy_info = strategy_manager.get_strategy_info('technical_reversal')
        print(f"策略名称: {strategy_info['name']}")
        print(f"策略描述: {strategy_info['description']}")
        
        print(f"\n2. 默认配置参数")
        print("-" * 30)
        config_params = strategy_info['config']
        for key, value in config_params.items():
            print(f"{key}: {value}")
        
        print(f"\n3. 执行策略选股")
        print("-" * 30)
        
        # 可以自定义配置
        custom_config = {
            'max_results': 10,  # 最多选择10只股票
            'min_price': 5.0,   # 最低价格5元
            'max_price': 30.0   # 最高价格30元
        }
        
        print("使用自定义配置执行策略...")
        results = strategy_manager.execute_strategy(
            'technical_reversal',
            config=custom_config,
            save_results=True
        )
        
        print(f"选股完成，共选中 {len(results)} 只股票")
        
        if results:
            print(f"\n4. 选股结果详情")
            print("-" * 30)
            
            for i, result in enumerate(results, 1):
                print(f"\n{i}. {result['stock_code']} - {result['stock_name']}")
                print(f"   评分: {result['score']:.2f}")
                print(f"   价格: {result['close_price']:.2f}元")
                print(f"   RSI: {result['rsi']:.1f}")
                print(f"   交易量比率: {result['volume_ratio']:.2f}")
                print(f"   价格位置: {result['price_position']:.2f}")
                print(f"   布林带位置: {result['bb_position']:.2f}")
                print(f"   满足条件数: {result['conditions_count']}")
                print(f"   选股原因: {result['reason']}")
        else:
            print("\n当前市场环境下未找到符合条件的股票")
            print("这可能是因为:")
            print("- 市场处于强势上涨阶段，缺乏技术反转机会")
            print("- 策略条件相对严格，确保选股质量")
            print("- 建议在市场调整时期使用该策略")
        
        print(f"\n5. 策略使用建议")
        print("-" * 30)
        print("• 适用场景: 震荡市场或调整后的反弹")
        print("• 持有期间: 建议5-10个交易日")
        print("• 止盈建议: 5-8%收益时考虑止盈")
        print("• 止损建议: 设置3-5%的止损位")
        print("• 风险控制: 分散投资，不要集中单一股票")
        
        print(f"\n6. 获取历史选股结果")
        print("-" * 30)
        historical_results = strategy_manager.get_strategy_results('technical_reversal')
        print(f"历史选股记录: {len(historical_results)} 条")
        
        if historical_results:
            print("最近的选股记录:")
            for result in historical_results[-3:]:  # 显示最近3条
                print(f"  {result['selection_date']}: {result['stock_code']} - {result.get('stock_name', 'N/A')} (评分: {result['score']:.2f})")
        
        return results
        
    except Exception as e:
        print(f"执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        data_access.close_connection()

def demonstrate_config_validation():
    """演示配置验证功能"""
    print(f"\n7. 配置验证示例")
    print("-" * 30)
    
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        strategy_manager = StrategyManager(data_access)
        
        # 测试有效配置
        valid_config = {
            'rsi_min': 25.0,
            'rsi_max': 40.0,
            'volume_ratio_min': 1.2,
            'volume_ratio_max': 3.0,
            'max_results': 20
        }
        
        is_valid = strategy_manager.validate_strategy_config('technical_reversal', valid_config)
        print(f"有效配置验证: {'通过' if is_valid else '失败'}")
        
        # 测试无效配置
        invalid_config = {
            'rsi_min': 50.0,  # 最小值大于最大值
            'rsi_max': 30.0,
            'volume_ratio_min': 3.0,  # 最小值大于最大值
            'volume_ratio_max': 1.0
        }
        
        is_valid = strategy_manager.validate_strategy_config('technical_reversal', invalid_config)
        print(f"无效配置验证: {'通过' if is_valid else '失败'}")
        
    except Exception as e:
        print(f"配置验证失败: {str(e)}")
    finally:
        data_access.close_connection()

def show_strategy_comparison():
    """显示与其他策略的对比"""
    print(f"\n8. 策略对比")
    print("-" * 30)
    
    print("技术反转策略 vs 交易量异动策略:")
    print("┌─────────────────┬─────────────────┬─────────────────┐")
    print("│     特征        │   技术反转策略   │  交易量异动策略  │")
    print("├─────────────────┼─────────────────┼─────────────────┤")
    print("│ 主要指标        │ RSI+布林带+均线  │    交易量放大    │")
    print("│ 适用市场        │    震荡/调整     │    任何市场      │")
    print("│ 持有期间        │    5-10天       │    3-7天        │")
    print("│ 风险等级        │      中等       │      较高       │")
    print("│ 胜率预期        │      较高       │      中等       │")
    print("│ 收益预期        │      稳健       │      较高       │")
    print("└─────────────────┴─────────────────┴─────────────────┘")

if __name__ == "__main__":
    # 运行主要示例
    results = run_technical_reversal_example()
    
    # 演示配置验证
    demonstrate_config_validation()
    
    # 显示策略对比
    show_strategy_comparison()
    
    print(f"\n示例运行完成!")
    print("="*60)
