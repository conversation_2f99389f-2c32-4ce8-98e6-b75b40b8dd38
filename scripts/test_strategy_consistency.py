#!/usr/bin/env python3
"""
测试策略一致性 - 验证同一天运行两次是否产生相同结果
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime, timedelta
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.validation.validation_manager import ValidationManager
import json
import time


def test_single_date_consistency():
    """测试单日验证的一致性"""
    print("=" * 80)
    print("测试单日验证一致性")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置测试参数
        strategy_name = "technical_reversal"
        test_date = datetime(2024, 4, 7)  # 使用一个固定的历史日期
        max_stocks = 15
        
        print(f"策略: {strategy_name}")
        print(f"测试日期: {test_date.date()}")
        print(f"最大选股数: {max_stocks}")
        print()
        
        # 第一次运行
        print("第一次运行...")
        start_time = time.time()
        summary1 = validation_manager.validate_single_date(
            strategy_name=strategy_name,
            test_date=test_date,
            max_stocks=max_stocks,
            save_results=False
        )
        time1 = time.time() - start_time
        
        # 等待一秒钟
        time.sleep(1)
        
        # 第二次运行
        print("第二次运行...")
        start_time = time.time()
        summary2 = validation_manager.validate_single_date(
            strategy_name=strategy_name,
            test_date=test_date,
            max_stocks=max_stocks,
            save_results=False
        )
        time2 = time.time() - start_time
        
        # 比较结果
        print("\n" + "=" * 80)
        print("结果比较")
        print("=" * 80)
        
        # 基本统计比较
        print(f"第一次运行:")
        print(f"  执行时间: {time1:.2f}秒")
        print(f"  总测试数: {summary1.total_tests}")
        print(f"  成功测试数: {summary1.successful_tests}")
        print(f"  失败测试数: {summary1.failed_tests}")
        if summary1.results:
            result1 = summary1.results[0]
            print(f"  选中股票数: {result1.selection_count}")
            print(f"  选股比例: {result1.selection_rate:.2f}%")
        
        print(f"\n第二次运行:")
        print(f"  执行时间: {time2:.2f}秒")
        print(f"  总测试数: {summary2.total_tests}")
        print(f"  成功测试数: {summary2.successful_tests}")
        print(f"  失败测试数: {summary2.failed_tests}")
        if summary2.results:
            result2 = summary2.results[0]
            print(f"  选中股票数: {result2.selection_count}")
            print(f"  选股比例: {result2.selection_rate:.2f}%")
        
        # 详细结果比较
        if summary1.results and summary2.results:
            result1 = summary1.results[0]
            result2 = summary2.results[0]
            
            print(f"\n详细结果比较:")
            print(f"选中股票数是否一致: {result1.selection_count == result2.selection_count}")
            print(f"选股比例是否一致: {abs(result1.selection_rate - result2.selection_rate) < 0.01}")
            
            # 比较选中的股票
            stocks1 = {stock['stock_code']: stock for stock in result1.selected_stocks}
            stocks2 = {stock['stock_code']: stock for stock in result2.selected_stocks}
            
            stocks1_codes = set(stocks1.keys())
            stocks2_codes = set(stocks2.keys())
            
            print(f"选中股票代码是否完全一致: {stocks1_codes == stocks2_codes}")
            
            if stocks1_codes == stocks2_codes:
                print("✓ 选中的股票代码完全一致")
                
                # 比较每只股票的详细信息
                all_scores_match = True
                all_indicators_match = True
                
                for code in stocks1_codes:
                    stock1 = stocks1[code]
                    stock2 = stocks2[code]
                    
                    # 比较评分
                    score_diff = abs(stock1.get('score', 0) - stock2.get('score', 0))
                    if score_diff > 0.01:
                        all_scores_match = False
                        print(f"  股票 {code} 评分不一致: {stock1.get('score', 0)} vs {stock2.get('score', 0)}")
                    
                    # 比较关键指标
                    for indicator in ['rsi', 'volume_ratio', 'close_price']:
                        val1 = stock1.get(indicator, 0)
                        val2 = stock2.get(indicator, 0)
                        if abs(val1 - val2) > 0.01:
                            all_indicators_match = False
                            print(f"  股票 {code} {indicator} 不一致: {val1} vs {val2}")
                
                print(f"所有股票评分是否一致: {all_scores_match}")
                print(f"所有关键指标是否一致: {all_indicators_match}")
                
                if all_scores_match and all_indicators_match:
                    print("✓ 完全一致！修复成功")
                    return True
                else:
                    print("✗ 存在不一致，需要进一步检查")
                    return False
            else:
                print("✗ 选中的股票不一致")
                print(f"  仅第一次选中: {stocks1_codes - stocks2_codes}")
                print(f"  仅第二次选中: {stocks2_codes - stocks1_codes}")
                return False
        else:
            print("✗ 无法比较结果，可能存在错误")
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        data_access.close_connection()


def test_multiple_runs_consistency():
    """测试多次运行的一致性"""
    print("\n" + "=" * 80)
    print("测试多次运行一致性")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置测试参数
        strategy_name = "technical_reversal"
        test_date = datetime(2024, 4, 30)  # 使用另一个固定的历史日期
        max_stocks = 10
        num_runs = 5
        
        print(f"策略: {strategy_name}")
        print(f"测试日期: {test_date.date()}")
        print(f"运行次数: {num_runs}")
        print()
        
        results = []
        
        for i in range(num_runs):
            print(f"第 {i+1} 次运行...")
            summary = validation_manager.validate_single_date(
                strategy_name=strategy_name,
                test_date=test_date,
                max_stocks=max_stocks,
                save_results=False
            )
            
            if summary.results:
                result = summary.results[0]
                results.append({
                    'run': i + 1,
                    'selection_count': result.selection_count,
                    'selection_rate': result.selection_rate,
                    'selected_stocks': [stock['stock_code'] for stock in result.selected_stocks]
                })
            
            time.sleep(0.5)  # 短暂等待
        
        # 分析一致性
        print(f"\n多次运行结果分析:")
        print("-" * 60)
        
        if results:
            # 检查选股数量一致性
            selection_counts = [r['selection_count'] for r in results]
            selection_rates = [r['selection_rate'] for r in results]
            
            print(f"选股数量: {selection_counts}")
            print(f"选股比例: {[f'{r:.2f}%' for r in selection_rates]}")
            
            counts_consistent = len(set(selection_counts)) == 1
            rates_consistent = all(abs(r - selection_rates[0]) < 0.01 for r in selection_rates)
            
            print(f"选股数量一致性: {counts_consistent}")
            print(f"选股比例一致性: {rates_consistent}")
            
            # 检查选中股票一致性
            first_stocks = set(results[0]['selected_stocks'])
            stocks_consistent = all(set(r['selected_stocks']) == first_stocks for r in results)
            
            print(f"选中股票一致性: {stocks_consistent}")
            
            if counts_consistent and rates_consistent and stocks_consistent:
                print("✓ 多次运行完全一致！")
                return True
            else:
                print("✗ 多次运行存在不一致")
                return False
        else:
            print("✗ 无有效结果")
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        data_access.close_connection()


def main():
    """主函数"""
    print("策略一致性测试")
    print("=" * 80)
    print("测试目标: 验证同一天运行策略验证两次是否产生相同结果")
    print()
    
    # 测试单日一致性
    single_test_passed = test_single_date_consistency()
    
    # 测试多次运行一致性
    multiple_test_passed = test_multiple_runs_consistency()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"单日验证一致性测试: {'通过' if single_test_passed else '失败'}")
    print(f"多次运行一致性测试: {'通过' if multiple_test_passed else '失败'}")
    
    if single_test_passed and multiple_test_passed:
        print("\n✓ 所有测试通过！策略验证结果现在是一致的。")
        print("\n修复说明:")
        print("1. 在策略执行时使用固定的目标日期而不是当前时间")
        print("2. 在结果生成时使用目标日期确保时间戳一致")
        print("3. 在验证过程中传递测试日期给策略分析方法")
    else:
        print("\n✗ 存在测试失败，需要进一步检查和修复。")
    
    return single_test_passed and multiple_test_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
