#!/usr/bin/env python3
"""
策略验证功能示例
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime, timedelta
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.validation.validation_manager import ValidationManager


def example_single_date_validation():
    """单日验证示例"""
    print("=" * 80)
    print("单日验证示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置验证参数
        strategy_name = "technical_reversal"
        test_date = datetime(2024, 4, 7)  # 选择一个特定的测试日期
        
        print(f"策略: {strategy_name}")
        print(f"测试日期: {test_date.date()}")
        
        # 执行单日验证
        summary = validation_manager.validate_single_date(
            strategy_name=strategy_name,
            test_date=test_date,
            max_stocks=15,
            save_results=False  # 示例不保存结果
        )
        
        # 生成报告
        report = validation_manager.validator.generate_validation_report(summary)
        print(report)
        
        return summary
        
    except Exception as e:
        print(f"单日验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def example_batch_validation():
    """批量验证示例"""
    print("\n" + "=" * 80)
    print("批量验证示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置验证参数
        strategy_name = "technical_reversal"
        start_date = datetime(2024, 3, 1)
        end_date = datetime(2024, 4, 30)
        frequency_days = 7  # 每周验证一次
        
        print(f"策略: {strategy_name}")
        print(f"验证期间: {start_date.date()} 到 {end_date.date()}")
        print(f"验证频率: 每 {frequency_days} 天")
        
        # 执行批量验证
        summary = validation_manager.validate_strategy_batch(
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            frequency_days=frequency_days,
            max_stocks=10,
            save_results=False  # 示例不保存结果
        )
        
        # 生成报告
        report = validation_manager.validator.generate_validation_report(summary)
        print(report)
        
        return summary
        
    except Exception as e:
        print(f"批量验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def example_specific_dates_validation():
    """指定日期验证示例"""
    print("\n" + "=" * 80)
    print("指定日期验证示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置验证参数
        strategy_name = "technical_reversal"
        test_dates = [
            datetime(2024, 4, 7),   # 第一个买入点
            datetime(2024, 4, 30),  # 第二个买入点
            datetime(2024, 3, 15),  # 其他测试日期
            datetime(2024, 5, 15)
        ]
        
        print(f"策略: {strategy_name}")
        print(f"指定测试日期: {[d.date() for d in test_dates]}")
        
        # 执行指定日期验证
        summary = validation_manager.validate_strategy_specific_dates(
            strategy_name=strategy_name,
            test_dates=test_dates,
            max_stocks=12,
            save_results=False  # 示例不保存结果
        )
        
        # 生成报告
        report = validation_manager.validator.generate_validation_report(summary)
        print(report)
        
        return summary
        
    except Exception as e:
        print(f"指定日期验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def example_strategy_comparison():
    """策略比较示例"""
    print("\n" + "=" * 80)
    print("策略比较示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置验证参数
        strategies = ["technical_reversal", "volume_anomaly"]
        start_date = datetime(2024, 3, 1)
        end_date = datetime(2024, 4, 30)
        frequency_days = 14  # 每两周验证一次
        
        print(f"策略: {', '.join(strategies)}")
        print(f"验证期间: {start_date.date()} 到 {end_date.date()}")
        print(f"验证频率: 每 {frequency_days} 天")
        
        # 执行策略比较
        results = validation_manager.compare_strategies(
            strategy_names=strategies,
            start_date=start_date,
            end_date=end_date,
            frequency_days=frequency_days,
            max_stocks=8,
            save_results=False  # 示例不保存结果
        )
        
        # 生成比较报告
        if results:
            comparison_report = validation_manager.generate_comparison_report(results)
            print(comparison_report)
            
            # 显示每个策略的详细信息
            print("\n详细验证结果:")
            print("=" * 80)
            for strategy_name, summary in results.items():
                print(f"\n{strategy_name} 策略详细报告:")
                print("-" * 60)
                detailed_report = validation_manager.validator.generate_validation_report(summary)
                print(detailed_report)
        
        return results
        
    except Exception as e:
        print(f"策略比较失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def example_stability_validation():
    """稳定性验证示例"""
    print("\n" + "=" * 80)
    print("稳定性验证示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置验证参数
        strategy_name = "technical_reversal"
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 6, 30)
        window_days = 30  # 30天滑动窗口
        
        print(f"策略: {strategy_name}")
        print(f"验证期间: {start_date.date()} 到 {end_date.date()}")
        print(f"滑动窗口: {window_days} 天")
        
        # 执行稳定性验证
        stability_analysis = validation_manager.validate_strategy_stability(
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            window_days=window_days
        )
        
        # 生成稳定性报告
        if stability_analysis:
            stability_report = validation_manager.generate_stability_report(stability_analysis)
            print(stability_report)
            
            # 显示详细的窗口结果
            print("\n详细窗口验证结果:")
            print("=" * 80)
            print(f"{'窗口期间':<25} {'测试次数':<8} {'成功次数':<8} {'平均选股':<8} {'选股比例':<8}")
            print("-" * 80)
            
            for result in stability_analysis['results']:
                summary = result['summary']
                print(f"{result['period']:<25} {summary.total_tests:<8} {summary.successful_tests:<8} "
                      f"{summary.avg_selection_count:<7.1f} {summary.avg_selection_rate:<7.2f}%")
        
        return stability_analysis
        
    except Exception as e:
        print(f"稳定性验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def main():
    """主函数"""
    print("策略验证功能示例")
    print("=" * 80)
    
    # 运行不同类型的验证示例
    
    # 1. 单日验证
    single_result = example_single_date_validation()
    
    # 2. 批量验证
    batch_result = example_batch_validation()
    
    # 3. 指定日期验证
    specific_result = example_specific_dates_validation()
    
    # 4. 策略比较
    comparison_results = example_strategy_comparison()
    
    # 5. 稳定性验证
    stability_result = example_stability_validation()
    
    print("\n" + "=" * 80)
    print("所有验证示例完成")
    print("=" * 80)
    
    # 总结
    print("\n验证功能总结:")
    print("1. 单日验证 - 测试策略在特定日期的选股表现")
    print("2. 批量验证 - 测试策略在一段时间内的选股稳定性")
    print("3. 指定日期验证 - 测试策略在关键时间点的表现")
    print("4. 策略比较 - 比较多个策略的验证结果")
    print("5. 稳定性验证 - 分析策略在不同时间窗口的稳定性")
    print("\n使用命令行工具进行更灵活的验证:")
    print("python scripts/run_validation.py technical_reversal batch --start-date 2024-01-01 --end-date 2024-06-30")


if __name__ == "__main__":
    main()
