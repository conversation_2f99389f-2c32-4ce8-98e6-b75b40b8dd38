#!/usr/bin/env python3
"""
分析股票601111从3月15日到现在的交易数据，寻找买入点特征
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config

def analyze_stock_601111():
    """分析股票601111的交易数据"""
    print("开始分析股票601111...")

    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)

    try:
        # 设置分析时间范围：3月15日到现在
        start_date = datetime(2024, 3, 15)
        end_date = datetime.now()

        print(f"分析时间范围: {start_date.date()} 到 {end_date.date()}")

        # 获取股票基本信息
        stock_info = data_access.get_stock_info('601111')
        if not stock_info:
            print("未找到股票601111的基本信息")
            return

        print(f"股票信息: {stock_info['stock_code']} - {stock_info['stock_name']}")

        # 获取交易数据
        trading_data = data_access.get_stock_data('601111', start_date, end_date)
        if not trading_data:
            print("未找到交易数据")
            return

        print(f"获取到{len(trading_data)}条交易数据")

        # 转换为DataFrame进行分析
        df = pd.DataFrame(trading_data)
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df = df.sort_values('trade_date')

        # 计算技术指标
        df = calculate_technical_indicators(df)

        # 分析买入点
        buy_signals = identify_buy_signals(df)

        # 输出分析结果
        print_analysis_results(df, buy_signals, stock_info)

        # 生成图表
        plot_analysis(df, buy_signals, stock_info)

        return df, buy_signals

    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        data_access.close_connection()

def calculate_technical_indicators(df):
    """计算技术指标"""
    # 确保数据类型正确
    for col in ['open_price', 'high_price', 'low_price', 'close_price', 'volume', 'turnover_rate']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # 移动平均线
    df['ma5'] = df['close_price'].rolling(window=5).mean()
    df['ma10'] = df['close_price'].rolling(window=10).mean()
    df['ma20'] = df['close_price'].rolling(window=20).mean()

    # 交易量移动平均
    df['vol_ma5'] = df['volume'].rolling(window=5).mean()
    df['vol_ma10'] = df['volume'].rolling(window=10).mean()

    # 价格变化率
    df['price_change'] = df['close_price'].pct_change()
    df['price_change_5d'] = df['close_price'].pct_change(5)

    # 交易量比率
    df['volume_ratio'] = df['volume'] / df['vol_ma10']

    # RSI指标
    df['rsi'] = calculate_rsi(df['close_price'], 14)

    # MACD指标
    df = calculate_macd(df)

    # 布林带
    df = calculate_bollinger_bands(df)

    # 支撑阻力位
    df['support'] = df['low_price'].rolling(window=20).min()
    df['resistance'] = df['high_price'].rolling(window=20).max()

    return df

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(df, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    ema_fast = df['close_price'].ewm(span=fast).mean()
    ema_slow = df['close_price'].ewm(span=slow).mean()
    df['macd'] = ema_fast - ema_slow
    df['macd_signal'] = df['macd'].ewm(span=signal).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    return df

def calculate_bollinger_bands(df, period=20, std_dev=2):
    """计算布林带"""
    df['bb_middle'] = df['close_price'].rolling(window=period).mean()
    bb_std = df['close_price'].rolling(window=period).std()
    df['bb_upper'] = df['bb_middle'] + (bb_std * std_dev)
    df['bb_lower'] = df['bb_middle'] - (bb_std * std_dev)
    df['bb_position'] = (df['close_price'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    return df

def identify_buy_signals(df):
    """识别买入信号"""
    buy_signals = []

    for i in range(20, len(df)):  # 从第20天开始，确保有足够的历史数据
        row = df.iloc[i]
        prev_row = df.iloc[i-1]

        # 买入信号条件
        conditions = []

        # 1. 价格突破条件
        if (row['close_price'] > row['ma5'] and
            prev_row['close_price'] <= prev_row['ma5']):
            conditions.append("突破5日均线")

        # 2. 交易量放大
        if row['volume_ratio'] > 1.5:
            conditions.append(f"交易量放大{row['volume_ratio']:.2f}倍")

        # 3. RSI超卖反弹
        if prev_row['rsi'] < 30 and row['rsi'] > 30:
            conditions.append("RSI超卖反弹")

        # 4. MACD金叉
        if (row['macd'] > row['macd_signal'] and
            prev_row['macd'] <= prev_row['macd_signal']):
            conditions.append("MACD金叉")

        # 5. 布林带下轨反弹
        if (prev_row['close_price'] <= prev_row['bb_lower'] and
            row['close_price'] > row['bb_lower']):
            conditions.append("布林带下轨反弹")

        # 6. 价格接近支撑位反弹
        if (row['close_price'] > row['support'] * 1.02 and
            row['close_price'] < row['support'] * 1.05):
            conditions.append("支撑位附近反弹")

        # 7. 连续下跌后的反弹
        if (df.iloc[i-2:i]['price_change'].sum() < -0.05 and
            row['price_change'] > 0.02):
            conditions.append("连续下跌后反弹")

        # 如果满足多个条件，记录为买入信号
        if len(conditions) >= 2:
            signal_strength = len(conditions)
            buy_signals.append({
                'date': row['trade_date'],
                'price': row['close_price'],
                'volume': row['volume'],
                'conditions': conditions,
                'strength': signal_strength,
                'rsi': row['rsi'],
                'macd': row['macd'],
                'volume_ratio': row['volume_ratio'],
                'bb_position': row['bb_position']
            })

    return buy_signals

def print_analysis_results(df, buy_signals, stock_info):
    """输出分析结果"""
    print("\n" + "="*60)
    print(f"股票 {stock_info['stock_code']} - {stock_info['stock_name']} 分析报告")
    print("="*60)

    # 基本统计
    print(f"\n基本统计信息:")
    print(f"分析期间: {df['trade_date'].min().date()} 到 {df['trade_date'].max().date()}")
    print(f"交易天数: {len(df)}")
    print(f"期间最高价: {df['high_price'].max():.2f}")
    print(f"期间最低价: {df['low_price'].min():.2f}")
    print(f"期间涨跌幅: {((df['close_price'].iloc[-1] / df['close_price'].iloc[0]) - 1) * 100:.2f}%")
    print(f"平均日交易量: {df['volume'].mean():.0f}")

    # 买入信号分析
    print(f"\n买入信号分析:")
    print(f"识别到 {len(buy_signals)} 个买入信号")

    if buy_signals:
        print("\n买入信号详情:")
        for i, signal in enumerate(buy_signals, 1):
            print(f"\n信号 {i}: {signal['date'].date()}")
            print(f"  价格: {signal['price']:.2f}")
            print(f"  信号强度: {signal['strength']}")
            print(f"  触发条件: {', '.join(signal['conditions'])}")
            print(f"  RSI: {signal['rsi']:.2f}")
            print(f"  交易量比率: {signal['volume_ratio']:.2f}")

            # 计算后续收益
            signal_date = signal['date']
            future_data = df[df['trade_date'] > signal_date].head(10)
            if not future_data.empty:
                max_return = ((future_data['high_price'].max() / signal['price']) - 1) * 100
                print(f"  后10日最大收益: {max_return:.2f}%")

def plot_analysis(df, buy_signals, stock_info):
    """生成分析图表"""
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))

    # 价格和均线图
    ax1.plot(df['trade_date'], df['close_price'], label='收盘价', linewidth=2)
    ax1.plot(df['trade_date'], df['ma5'], label='MA5', alpha=0.7)
    ax1.plot(df['trade_date'], df['ma10'], label='MA10', alpha=0.7)
    ax1.plot(df['trade_date'], df['ma20'], label='MA20', alpha=0.7)

    # 布林带
    ax1.fill_between(df['trade_date'], df['bb_upper'], df['bb_lower'], alpha=0.1, color='gray')
    ax1.plot(df['trade_date'], df['bb_upper'], '--', alpha=0.5, color='gray')
    ax1.plot(df['trade_date'], df['bb_lower'], '--', alpha=0.5, color='gray')

    # 标记买入信号
    for signal in buy_signals:
        ax1.scatter(signal['date'], signal['price'], color='red', s=100, marker='^',
                   label='买入信号' if signal == buy_signals[0] else "")

    ax1.set_title(f"{stock_info['stock_name']} ({stock_info['stock_code']}) 价格走势")
    ax1.set_ylabel('价格 (元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 交易量图
    ax2.bar(df['trade_date'], df['volume'], alpha=0.6, label='交易量')
    ax2.plot(df['trade_date'], df['vol_ma10'], color='red', label='10日均量')

    # 标记买入信号对应的交易量
    for signal in buy_signals:
        ax2.scatter(signal['date'], signal['volume'], color='red', s=100, marker='^')

    ax2.set_ylabel('交易量')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # RSI和MACD图
    ax3_twin = ax3.twinx()
    ax3.plot(df['trade_date'], df['rsi'], label='RSI', color='purple')
    ax3.axhline(y=30, color='red', linestyle='--', alpha=0.5)
    ax3.axhline(y=70, color='red', linestyle='--', alpha=0.5)
    ax3.set_ylabel('RSI')
    ax3.set_ylim(0, 100)

    ax3_twin.plot(df['trade_date'], df['macd'], label='MACD', color='blue')
    ax3_twin.plot(df['trade_date'], df['macd_signal'], label='MACD Signal', color='orange')
    ax3_twin.set_ylabel('MACD')

    # 标记买入信号
    for signal in buy_signals:
        ax3.scatter(signal['date'], signal['rsi'], color='red', s=50, marker='^')

    ax3.legend(loc='upper left')
    ax3_twin.legend(loc='upper right')
    ax3.grid(True, alpha=0.3)

    # 设置x轴格式
    for ax in [ax1, ax2, ax3]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    plt.savefig('scripts/601111_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\n图表已保存到: scripts/601111_analysis.png")
    plt.show()

if __name__ == "__main__":
    analyze_stock_601111()
