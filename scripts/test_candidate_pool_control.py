#!/usr/bin/env python3
"""
测试候选股票池大小控制功能
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.validation.validation_manager import ValidationManager


def test_different_pool_sizes():
    """测试不同候选股票池大小的效果"""
    print("=" * 80)
    print("测试候选股票池大小控制功能")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置测试参数
        strategy_name = "technical_reversal"
        test_date = datetime(2024, 4, 7)
        max_stocks = 10
        
        # 测试不同的候选股票池大小
        pool_sizes = [50, 100, 200, 500, None]  # None表示使用所有股票
        
        print(f"策略: {strategy_name}")
        print(f"测试日期: {test_date.date()}")
        print(f"最大选股数: {max_stocks}")
        print()
        
        results = []
        
        for pool_size in pool_sizes:
            print(f"测试候选股票池大小: {pool_size if pool_size else '全部股票'}")
            
            summary = validation_manager.validate_single_date(
                strategy_name=strategy_name,
                test_date=test_date,
                max_stocks=max_stocks,
                candidate_pool_size=pool_size,
                save_results=False
            )
            
            if summary.results:
                result = summary.results[0]
                results.append({
                    'pool_size': pool_size if pool_size else '全部',
                    'total_candidates': result.total_candidates,
                    'selection_count': result.selection_count,
                    'selection_rate': result.selection_rate,
                    'execution_time': result.execution_time,
                    'selected_stocks': [stock['stock_code'] for stock in result.selected_stocks]
                })
                
                print(f"  候选股票数: {result.total_candidates}")
                print(f"  选中股票数: {result.selection_count}")
                print(f"  选股比例: {result.selection_rate:.2f}%")
                print(f"  执行时间: {result.execution_time:.2f}秒")
                if result.selected_stocks:
                    print(f"  选中股票: {', '.join([stock['stock_code'] for stock in result.selected_stocks[:5]])}")
                    if len(result.selected_stocks) > 5:
                        print(f"           ... 还有 {len(result.selected_stocks) - 5} 只")
                print()
        
        # 分析结果
        print("=" * 80)
        print("结果分析")
        print("=" * 80)
        
        print(f"{'候选池大小':<12} {'候选股票数':<10} {'选中数量':<8} {'选股比例':<10} {'执行时间':<10}")
        print("-" * 60)
        
        for result in results:
            print(f"{str(result['pool_size']):<12} "
                  f"{result['total_candidates']:<10} "
                  f"{result['selection_count']:<8} "
                  f"{result['selection_rate']:<9.2f}% "
                  f"{result['execution_time']:<9.2f}s")
        
        # 性能分析
        print(f"\n性能分析:")
        if len(results) >= 2:
            fastest = min(results, key=lambda x: x['execution_time'])
            slowest = max(results, key=lambda x: x['execution_time'])
            
            print(f"最快执行: 候选池大小 {fastest['pool_size']} ({fastest['execution_time']:.2f}秒)")
            print(f"最慢执行: 候选池大小 {slowest['pool_size']} ({slowest['execution_time']:.2f}秒)")
            print(f"性能提升: {(slowest['execution_time'] / fastest['execution_time']):.1f}倍")
        
        # 选股一致性分析
        print(f"\n选股一致性分析:")
        if len(results) >= 2:
            # 比较不同池大小选出的股票重叠情况
            base_stocks = set(results[0]['selected_stocks'])
            
            for i, result in enumerate(results[1:], 1):
                current_stocks = set(result['selected_stocks'])
                overlap = base_stocks & current_stocks
                overlap_rate = len(overlap) / len(base_stocks) * 100 if base_stocks else 0
                
                print(f"候选池 {results[0]['pool_size']} vs {result['pool_size']}: "
                      f"重叠 {len(overlap)} 只股票 ({overlap_rate:.1f}%)")
        
        return results
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def test_specific_stock_pool():
    """测试指定股票池功能"""
    print("\n" + "=" * 80)
    print("测试指定股票池功能")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置测试参数
        strategy_name = "technical_reversal"
        test_date = datetime(2024, 4, 7)
        max_stocks = 5
        
        # 指定一个小的股票池进行测试
        specific_stocks = ['000001', '000002', '600000', '600036', '000858', 
                          '002415', '300059', '600519', '000858', '002304']
        
        print(f"策略: {strategy_name}")
        print(f"测试日期: {test_date.date()}")
        print(f"指定股票池: {', '.join(specific_stocks)}")
        print()
        
        summary = validation_manager.validate_single_date(
            strategy_name=strategy_name,
            test_date=test_date,
            max_stocks=max_stocks,
            stock_pool=specific_stocks,
            save_results=False
        )
        
        if summary.results:
            result = summary.results[0]
            
            print(f"候选股票数: {result.total_candidates}")
            print(f"选中股票数: {result.selection_count}")
            print(f"选股比例: {result.selection_rate:.2f}%")
            print(f"执行时间: {result.execution_time:.2f}秒")
            
            if result.selected_stocks:
                print(f"\n选中股票详情:")
                print(f"{'股票代码':<10} {'股票名称':<12} {'评分':<8} {'RSI':<8} {'量比':<8}")
                print("-" * 50)
                
                for stock in result.selected_stocks:
                    print(f"{stock.get('stock_code', ''):<10} "
                          f"{stock.get('stock_name', ''):<12} "
                          f"{stock.get('score', 0):<7.1f} "
                          f"{stock.get('rsi', 0):<7.1f} "
                          f"{stock.get('volume_ratio', 0):<7.2f}")
            else:
                print("未选中任何股票")
        
        return summary
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def test_batch_validation_with_pool_control():
    """测试批量验证中的候选股票池控制"""
    print("\n" + "=" * 80)
    print("测试批量验证中的候选股票池控制")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建验证管理器
        validation_manager = ValidationManager(data_access)
        
        # 设置测试参数
        strategy_name = "technical_reversal"
        start_date = datetime(2024, 4, 1)
        end_date = datetime(2024, 4, 30)
        frequency_days = 14
        candidate_pool_size = 100  # 限制为100只股票
        
        print(f"策略: {strategy_name}")
        print(f"验证期间: {start_date.date()} 到 {end_date.date()}")
        print(f"验证频率: 每 {frequency_days} 天")
        print(f"候选股票池大小: {candidate_pool_size}")
        print()
        
        summary = validation_manager.validate_strategy_batch(
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            frequency_days=frequency_days,
            max_stocks=8,
            candidate_pool_size=candidate_pool_size,
            save_results=False
        )
        
        # 生成报告
        report = validation_manager.validator.generate_validation_report(summary)
        print(report)
        
        return summary
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def main():
    """主函数"""
    print("候选股票池大小控制功能测试")
    print("=" * 80)
    print("测试目标: 验证候选股票池大小控制功能是否正常工作")
    print()
    
    # 测试不同候选股票池大小
    pool_test_results = test_different_pool_sizes()
    
    # 测试指定股票池
    specific_pool_results = test_specific_stock_pool()
    
    # 测试批量验证中的候选股票池控制
    batch_results = test_batch_validation_with_pool_control()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    success_count = 0
    total_tests = 3
    
    if pool_test_results:
        print("✓ 不同候选股票池大小测试: 通过")
        success_count += 1
    else:
        print("✗ 不同候选股票池大小测试: 失败")
    
    if specific_pool_results:
        print("✓ 指定股票池测试: 通过")
        success_count += 1
    else:
        print("✗ 指定股票池测试: 失败")
    
    if batch_results:
        print("✓ 批量验证候选股票池控制测试: 通过")
        success_count += 1
    else:
        print("✗ 批量验证候选股票池控制测试: 失败")
    
    print(f"\n测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("\n✓ 所有测试通过！候选股票池大小控制功能正常工作。")
        print("\n功能说明:")
        print("1. 可以通过 candidate_pool_size 参数控制候选股票池大小")
        print("2. 可以通过 stock_pool 参数指定特定的股票池")
        print("3. 较小的候选股票池可以显著提高验证速度")
        print("4. 使用固定随机种子确保相同配置下结果一致")
    else:
        print("\n✗ 存在测试失败，需要进一步检查。")
    
    return success_count == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
