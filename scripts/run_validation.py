#!/usr/bin/env python3
"""
策略验证命令行工具
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import argparse
from datetime import datetime, timed<PERSON>ta
from typing import List

from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.validation.validation_manager import ValidationManager


def parse_date(date_str: str) -> datetime:
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 格式")


def parse_date_list(date_str: str) -> List[datetime]:
    """解析日期列表"""
    if not date_str:
        return []
    dates = []
    for date_part in date_str.split(','):
        dates.append(parse_date(date_part.strip()))
    return dates


def parse_stock_list(stock_str: str) -> List[str]:
    """解析股票列表"""
    if not stock_str:
        return None
    return [code.strip() for code in stock_str.split(',') if code.strip()]


def main():
    parser = argparse.ArgumentParser(description='策略验证工具')

    # 基本参数
    parser.add_argument('strategy', help='策略名称 (如: technical_reversal)')

    # 验证模式
    subparsers = parser.add_subparsers(dest='mode', help='验证模式')

    # 批量验证模式
    batch_parser = subparsers.add_parser('batch', help='批量验证')
    batch_parser.add_argument('--start-date', type=parse_date, required=True,
                             help='验证开始日期 (YYYY-MM-DD)')
    batch_parser.add_argument('--end-date', type=parse_date, required=True,
                             help='验证结束日期 (YYYY-MM-DD)')
    batch_parser.add_argument('--frequency', type=int, default=7,
                             help='验证频率（天）(默认: 7)')

    # 指定日期验证模式
    dates_parser = subparsers.add_parser('dates', help='指定日期验证')
    dates_parser.add_argument('--test-dates', type=parse_date_list, required=True,
                             help='测试日期列表，用逗号分隔 (如: 2024-01-01,2024-02-01)')

    # 单日验证模式
    single_parser = subparsers.add_parser('single', help='单日验证')
    single_parser.add_argument('--test-date', type=parse_date, required=True,
                              help='测试日期 (YYYY-MM-DD)')

    # 策略比较模式
    compare_parser = subparsers.add_parser('compare', help='策略比较')
    compare_parser.add_argument('--strategies', required=True,
                               help='策略列表，用逗号分隔 (如: technical_reversal,volume_anomaly)')
    compare_parser.add_argument('--start-date', type=parse_date, required=True,
                               help='验证开始日期 (YYYY-MM-DD)')
    compare_parser.add_argument('--end-date', type=parse_date, required=True,
                               help='验证结束日期 (YYYY-MM-DD)')
    compare_parser.add_argument('--frequency', type=int, default=7,
                               help='验证频率（天）(默认: 7)')

    # 稳定性验证模式
    stability_parser = subparsers.add_parser('stability', help='稳定性验证')
    stability_parser.add_argument('--start-date', type=parse_date, required=True,
                                 help='验证开始日期 (YYYY-MM-DD)')
    stability_parser.add_argument('--end-date', type=parse_date, required=True,
                                 help='验证结束日期 (YYYY-MM-DD)')
    stability_parser.add_argument('--window-days', type=int, default=30,
                                 help='滑动窗口天数 (默认: 30)')

    # 通用参数
    for subparser in [batch_parser, dates_parser, single_parser, compare_parser, stability_parser]:
        subparser.add_argument('--max-stocks', type=int, default=20,
                              help='每次测试最多选股数 (默认: 20)')
        subparser.add_argument('--stock-pool', type=parse_stock_list,
                              help='股票池，用逗号分隔 (如: 000001,000002,600000)')
        subparser.add_argument('--candidate-pool-size', type=int,
                              help='候选股票池大小，None表示使用所有股票 (如: 100, 500, 1000)')
        subparser.add_argument('--no-save', action='store_true',
                              help='不保存验证结果到数据库')
        subparser.add_argument('--output', help='输出报告到文件')
        subparser.add_argument('--verbose', '-v', action='store_true',
                              help='详细输出')

    args = parser.parse_args()

    # 检查模式
    if not args.mode:
        parser.print_help()
        return 1

    try:
        # 初始化数据访问
        config = db_config.get_mysql_config()
        data_access = MySQLDataAccess(**config)

        # 创建验证管理器
        validation_manager = ValidationManager(data_access)

        print("=" * 80)
        print("策略验证工具")
        print("=" * 80)

        report = ""

        if args.mode == 'batch':
            # 批量验证
            print(f"开始批量验证策略: {args.strategy}")
            print(f"验证期间: {args.start_date.date()} 到 {args.end_date.date()}")
            print(f"验证频率: 每 {args.frequency} 天")

            summary = validation_manager.validate_strategy_batch(
                strategy_name=args.strategy,
                start_date=args.start_date,
                end_date=args.end_date,
                frequency_days=args.frequency,
                max_stocks=args.max_stocks,
                stock_pool=args.stock_pool,
                candidate_pool_size=args.candidate_pool_size,
                save_results=not args.no_save
            )

            report = validation_manager.validator.generate_validation_report(summary)

        elif args.mode == 'dates':
            # 指定日期验证
            print(f"开始验证策略: {args.strategy}")
            print(f"指定测试日期: {[d.date() for d in args.test_dates]}")

            summary = validation_manager.validate_strategy_specific_dates(
                strategy_name=args.strategy,
                test_dates=args.test_dates,
                max_stocks=args.max_stocks,
                stock_pool=args.stock_pool,
                candidate_pool_size=args.candidate_pool_size,
                save_results=not args.no_save
            )

            report = validation_manager.validator.generate_validation_report(summary)

        elif args.mode == 'single':
            # 单日验证
            print(f"开始验证策略: {args.strategy}")
            print(f"测试日期: {args.test_date.date()}")

            summary = validation_manager.validate_single_date(
                strategy_name=args.strategy,
                test_date=args.test_date,
                max_stocks=args.max_stocks,
                stock_pool=args.stock_pool,
                candidate_pool_size=args.candidate_pool_size,
                save_results=not args.no_save
            )

            report = validation_manager.validator.generate_validation_report(summary)

        elif args.mode == 'compare':
            # 策略比较
            strategies = [s.strip() for s in args.strategies.split(',')]
            print(f"开始比较策略: {', '.join(strategies)}")
            print(f"验证期间: {args.start_date.date()} 到 {args.end_date.date()}")

            results = validation_manager.compare_strategies(
                strategy_names=strategies,
                start_date=args.start_date,
                end_date=args.end_date,
                frequency_days=args.frequency,
                max_stocks=args.max_stocks,
                stock_pool=args.stock_pool,
                candidate_pool_size=args.candidate_pool_size,
                save_results=not args.no_save
            )

            # 生成比较报告
            report = validation_manager.generate_comparison_report(results)

            # 如果需要详细报告
            if args.verbose:
                report += "\n\n" + "=" * 80
                report += "\n详细验证报告"
                report += "\n" + "=" * 80

                for strategy_name, summary in results.items():
                    report += f"\n\n{strategy_name} 策略详细报告:"
                    report += "\n" + "-" * 60
                    detailed_report = validation_manager.validator.generate_validation_report(summary)
                    report += "\n" + detailed_report

        elif args.mode == 'stability':
            # 稳定性验证
            print(f"开始稳定性验证策略: {args.strategy}")
            print(f"验证期间: {args.start_date.date()} 到 {args.end_date.date()}")
            print(f"滑动窗口: {args.window_days} 天")

            stability_analysis = validation_manager.validate_strategy_stability(
                strategy_name=args.strategy,
                start_date=args.start_date,
                end_date=args.end_date,
                window_days=args.window_days
            )

            if stability_analysis:
                report = validation_manager.generate_stability_report(stability_analysis)

                # 显示详细的窗口结果
                if args.verbose:
                    report += "\n\n" + "=" * 80
                    report += "\n详细窗口验证结果"
                    report += "\n" + "=" * 80

                    for result in stability_analysis['results']:
                        report += f"\n\n{result['period']}:"
                        report += "\n" + "-" * 40
                        summary = result['summary']
                        report += f"\n测试次数: {summary.total_tests}"
                        report += f"\n成功次数: {summary.successful_tests}"
                        report += f"\n平均选股: {summary.avg_selection_count:.1f}"
                        report += f"\n选股比例: {summary.avg_selection_rate:.2f}%"
            else:
                report = "稳定性验证失败"

        # 输出报告
        print(report)

        # 保存报告到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\n报告已保存到: {args.output}")

        return 0

    except Exception as e:
        print(f"验证失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

    finally:
        try:
            data_access.close_connection()
        except:
            pass


if __name__ == "__main__":
    exit(main())
