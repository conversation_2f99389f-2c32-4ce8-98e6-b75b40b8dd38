#!/usr/bin/env python3
"""
测试技术反转选股策略
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.strategies.strategy_manager import StrategyManager

def test_technical_reversal_strategy():
    """测试技术反转策略"""
    print("测试技术反转选股策略...")
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建策略管理器
        strategy_manager = StrategyManager(data_access)
        
        # 获取可用策略
        available_strategies = strategy_manager.get_available_strategies()
        print(f"可用策略: {available_strategies}")
        
        # 检查技术反转策略是否已注册
        if 'technical_reversal' not in available_strategies:
            print("错误: 技术反转策略未注册")
            return
        
        # 获取策略信息
        strategy_info = strategy_manager.get_strategy_info('technical_reversal')
        print(f"\n策略信息:")
        print(f"名称: {strategy_info['name']}")
        print(f"描述: {strategy_info['description']}")
        print(f"默认配置: {strategy_info['config']}")
        
        # 测试策略配置验证
        test_config = {
            'rsi_min': 25.0,
            'rsi_max': 40.0,
            'volume_ratio_min': 1.3,
            'volume_ratio_max': 3.0,
            'max_results': 10
        }
        
        is_valid = strategy_manager.validate_strategy_config('technical_reversal', test_config)
        print(f"\n测试配置验证: {'通过' if is_valid else '失败'}")
        
        # 执行策略（使用默认配置）
        print(f"\n开始执行技术反转策略...")
        start_time = datetime.now()
        
        results = strategy_manager.execute_strategy(
            'technical_reversal',
            config=test_config,
            save_results=True
        )
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        print(f"策略执行完成，耗时: {execution_time:.2f}秒")
        print(f"选中股票数量: {len(results)}")
        
        # 显示结果
        if results:
            print(f"\n选股结果:")
            print("-" * 80)
            for i, result in enumerate(results, 1):
                print(f"{i:2d}. {result['stock_code']} - {result['stock_name']}")
                print(f"    评分: {result['score']:.2f}")
                print(f"    价格: {result['close_price']:.2f}元")
                print(f"    RSI: {result['rsi']:.1f}")
                print(f"    交易量比率: {result['volume_ratio']:.2f}")
                print(f"    价格位置: {result['price_position']:.2f}")
                print(f"    布林带位置: {result['bb_position']:.2f}")
                print(f"    满足条件数: {result['conditions_count']}")
                print(f"    原因: {result['reason']}")
                print()
        else:
            print("未找到符合条件的股票")
        
        # 测试格式化输出
        formatted_results = strategy_manager.format_results(results, show_details=True)
        print("\n格式化结果:")
        print(formatted_results)
        
        # 测试获取历史结果
        print("\n测试获取历史结果...")
        historical_results = strategy_manager.get_strategy_results('technical_reversal')
        print(f"历史结果数量: {len(historical_results)}")
        
        return results
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        data_access.close_connection()

def analyze_strategy_performance(results):
    """分析策略表现"""
    if not results:
        print("没有结果可分析")
        return
    
    print("\n策略表现分析:")
    print("-" * 50)
    
    # 评分分布
    scores = [r['score'] for r in results]
    print(f"评分范围: {min(scores):.2f} - {max(scores):.2f}")
    print(f"平均评分: {sum(scores)/len(scores):.2f}")
    
    # RSI分布
    rsi_values = [r['rsi'] for r in results]
    print(f"RSI范围: {min(rsi_values):.1f} - {max(rsi_values):.1f}")
    print(f"平均RSI: {sum(rsi_values)/len(rsi_values):.1f}")
    
    # 交易量比率分布
    volume_ratios = [r['volume_ratio'] for r in results]
    print(f"交易量比率范围: {min(volume_ratios):.2f} - {max(volume_ratios):.2f}")
    print(f"平均交易量比率: {sum(volume_ratios)/len(volume_ratios):.2f}")
    
    # 价格分布
    prices = [r['close_price'] for r in results]
    print(f"价格范围: {min(prices):.2f} - {max(prices):.2f}元")
    print(f"平均价格: {sum(prices)/len(prices):.2f}元")
    
    # 条件满足情况
    conditions_counts = [r['conditions_count'] for r in results]
    print(f"满足条件数范围: {min(conditions_counts)} - {max(conditions_counts)}")
    print(f"平均满足条件数: {sum(conditions_counts)/len(conditions_counts):.1f}")

def compare_with_volume_strategy():
    """与交易量异动策略对比"""
    print("\n与交易量异动策略对比...")
    
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        strategy_manager = StrategyManager(data_access)
        
        # 执行两个策略
        print("执行技术反转策略...")
        technical_results = strategy_manager.execute_strategy('technical_reversal', save_results=False)
        
        print("执行交易量异动策略...")
        volume_results = strategy_manager.execute_strategy('volume_anomaly', save_results=False)
        
        print(f"\n对比结果:")
        print(f"技术反转策略选中: {len(technical_results)}只")
        print(f"交易量异动策略选中: {len(volume_results)}只")
        
        # 找出重叠的股票
        technical_codes = {r['stock_code'] for r in technical_results}
        volume_codes = {r['stock_code'] for r in volume_results}
        
        overlap = technical_codes & volume_codes
        print(f"重叠股票: {len(overlap)}只")
        
        if overlap:
            print("重叠股票列表:")
            for code in overlap:
                tech_result = next(r for r in technical_results if r['stock_code'] == code)
                vol_result = next(r for r in volume_results if r['stock_code'] == code)
                print(f"  {code} - {tech_result['stock_name']}")
                print(f"    技术反转评分: {tech_result['score']:.2f}")
                print(f"    交易量异动评分: {vol_result['score']:.2f}")
        
        # 各自独有的股票
        technical_only = technical_codes - volume_codes
        volume_only = volume_codes - technical_codes
        
        print(f"\n技术反转策略独有: {len(technical_only)}只")
        print(f"交易量异动策略独有: {len(volume_only)}只")
        
    except Exception as e:
        print(f"对比失败: {str(e)}")
    finally:
        data_access.close_connection()

if __name__ == "__main__":
    print("="*80)
    print("技术反转选股策略测试")
    print("="*80)
    
    # 测试策略
    results = test_technical_reversal_strategy()
    
    if results:
        # 分析表现
        analyze_strategy_performance(results)
        
        # 与其他策略对比
        compare_with_volume_strategy()
    
    print("\n测试完成!")
