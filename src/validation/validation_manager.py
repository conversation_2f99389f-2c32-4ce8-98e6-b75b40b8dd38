"""
验证管理器
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from ..core.interfaces.strategy_validator import ValidationConfig, ValidationSummary
from ..core.interfaces.data_access import IDataAccess
from .strategy_validator import StrategyValidator


class ValidationManager:
    """验证管理器"""

    def __init__(self, data_access: IDataAccess):
        self.data_access = data_access
        self.validator = StrategyValidator(data_access)
        self.logger = logging.getLogger(__name__)

    def validate_strategy_batch(self, strategy_name: str,
                              start_date: datetime,
                              end_date: datetime,
                              frequency_days: int = 7,
                              **kwargs) -> ValidationSummary:
        """批量验证策略"""

        # 生成测试日期列表
        test_dates = self._generate_test_dates(start_date, end_date, frequency_days)

        # 创建验证配置
        config = ValidationConfig(
            strategy_name=strategy_name,
            test_dates=test_dates,
            max_stocks_per_test=kwargs.get('max_stocks', 20),
            stock_pool=kwargs.get('stock_pool', None),
            show_indicators=kwargs.get('show_indicators', True),
            save_results=kwargs.get('save_results', True),
            candidate_pool_size=kwargs.get('candidate_pool_size', None)
        )

        self.logger.info(f"开始批量验证策略: {strategy_name}")
        self.logger.info(f"验证期间: {start_date.date()} 到 {end_date.date()}")
        self.logger.info(f"测试频率: 每 {frequency_days} 天")
        self.logger.info(f"测试日期数量: {len(test_dates)}")

        # 执行验证
        summary = self.validator.validate_strategy(config)

        return summary

    def validate_strategy_specific_dates(self, strategy_name: str,
                                       test_dates: List[datetime],
                                       **kwargs) -> ValidationSummary:
        """验证策略在指定日期"""

        # 导入必要的类
        from ..core.interfaces.strategy_validator import ValidationSummary, ValidationConfig

        # 过滤掉未来的日期
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        valid_test_dates = []
        future_dates = []

        for test_date in test_dates:
            if test_date <= today:
                valid_test_dates.append(test_date)
            else:
                future_dates.append(test_date)

        # 如果有未来日期，给出警告
        if future_dates:
            future_dates_str = ', '.join([d.date().strftime('%Y-%m-%d') for d in future_dates])
            self.logger.warning(f"以下测试日期超过当前日期，已忽略: {future_dates_str}")

        # 如果没有有效的测试日期，返回空结果
        if not valid_test_dates:
            self.logger.warning("没有有效的测试日期，无法进行验证")
            # 创建一个虚拟的配置（使用今天的日期作为占位符）
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            dummy_config = ValidationConfig(
                strategy_name=strategy_name,
                test_dates=[today],  # 使用今天作为占位符
                max_stocks_per_test=kwargs.get('max_stocks', 20),
                stock_pool=kwargs.get('stock_pool', None),
                show_indicators=kwargs.get('show_indicators', True),
                save_results=kwargs.get('save_results', True),
                candidate_pool_size=kwargs.get('candidate_pool_size', None)
            )
            # 但是返回空的汇总结果
            return ValidationSummary(
                config=dummy_config,
                results=[],
                total_tests=0,
                successful_tests=0,
                failed_tests=0,
                avg_selection_count=0.0,
                avg_selection_rate=0.0,
                total_execution_time=0.0
            )

        # 创建验证配置
        config = ValidationConfig(
            strategy_name=strategy_name,
            test_dates=valid_test_dates,
            max_stocks_per_test=kwargs.get('max_stocks', 20),
            stock_pool=kwargs.get('stock_pool', None),
            show_indicators=kwargs.get('show_indicators', True),
            save_results=kwargs.get('save_results', True),
            candidate_pool_size=kwargs.get('candidate_pool_size', None)
        )

        self.logger.info(f"开始验证策略: {strategy_name}")
        self.logger.info(f"有效测试日期数量: {len(valid_test_dates)}")
        if future_dates:
            self.logger.info(f"忽略的未来日期数量: {len(future_dates)}")

        # 执行验证
        summary = self.validator.validate_strategy(config)

        return summary

    def validate_single_date(self, strategy_name: str,
                           test_date: datetime,
                           **kwargs) -> ValidationSummary:
        """验证单个日期"""

        return self.validate_strategy_specific_dates(
            strategy_name=strategy_name,
            test_dates=[test_date],
            **kwargs
        )

    def compare_strategies(self, strategy_names: List[str],
                         start_date: datetime,
                         end_date: datetime,
                         frequency_days: int = 7,
                         **kwargs) -> Dict[str, ValidationSummary]:
        """比较多个策略"""

        results = {}

        for strategy_name in strategy_names:
            try:
                self.logger.info(f"开始验证策略: {strategy_name}")
                summary = self.validate_strategy_batch(
                    strategy_name=strategy_name,
                    start_date=start_date,
                    end_date=end_date,
                    frequency_days=frequency_days,
                    **kwargs
                )
                results[strategy_name] = summary
                self.logger.info(f"策略 {strategy_name} 验证完成")

            except Exception as e:
                self.logger.error(f"策略 {strategy_name} 验证失败: {str(e)}")
                continue

        return results

    def generate_comparison_report(self, results: Dict[str, ValidationSummary]) -> str:
        """生成策略比较报告"""
        if not results:
            return "没有验证结果可比较"

        report = []
        report.append("=" * 100)
        report.append("策略验证比较报告")
        report.append("=" * 100)

        # 表头
        report.append(f"\n{'策略名称':<20} {'测试次数':<8} {'成功率':<8} {'平均选股':<10} {'平均比例':<10} {'执行时间':<10}")
        report.append("-" * 100)

        # 策略数据
        for strategy_name, summary in results.items():
            success_rate = summary.successful_tests / summary.total_tests * 100 if summary.total_tests > 0 else 0
            report.append(
                f"{strategy_name:<20} "
                f"{summary.total_tests:<8} "
                f"{success_rate:<7.1f}% "
                f"{summary.avg_selection_count:<9.1f} "
                f"{summary.avg_selection_rate:<9.2f}% "
                f"{summary.total_execution_time:<9.1f}s"
            )

        # 排名
        report.append(f"\n策略排名:")
        report.append("-" * 50)

        # 按成功率排名
        sorted_by_success = sorted(results.items(),
                                 key=lambda x: x[1].successful_tests / x[1].total_tests if x[1].total_tests > 0 else 0,
                                 reverse=True)
        report.append(f"按成功率排名:")
        for i, (name, summary) in enumerate(sorted_by_success, 1):
            success_rate = summary.successful_tests / summary.total_tests * 100 if summary.total_tests > 0 else 0
            report.append(f"  {i}. {name}: {success_rate:.1f}%")

        # 按平均选股数量排名
        sorted_by_selection = sorted(results.items(),
                                   key=lambda x: x[1].avg_selection_count,
                                   reverse=True)
        report.append(f"\n按平均选股数量排名:")
        for i, (name, summary) in enumerate(sorted_by_selection, 1):
            report.append(f"  {i}. {name}: {summary.avg_selection_count:.1f}")

        return "\n".join(report)

    def _generate_test_dates(self, start_date: datetime,
                           end_date: datetime,
                           frequency_days: int) -> List[datetime]:
        """生成测试日期列表"""
        test_dates = []
        current_date = start_date

        # 确保结束日期不超过当前日期
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        actual_end_date = min(end_date, today)

        # 如果开始日期就超过了当前日期，返回空列表
        if start_date > today:
            self.logger.warning(f"开始日期 {start_date.date()} 超过当前日期 {today.date()}，无法生成测试日期")
            return test_dates

        while current_date <= actual_end_date:
            # 只在工作日进行测试
            if current_date.weekday() < 5:  # 周一到周五
                test_dates.append(current_date)

            current_date += timedelta(days=frequency_days)

        # 如果原始结束日期超过当前日期，给出提示
        if end_date > today:
            self.logger.info(f"结束日期 {end_date.date()} 超过当前日期 {today.date()}，已调整为 {actual_end_date.date()}")

        return test_dates

    def get_recent_validation_summary(self, strategy_name: str,
                                    days: int = 30) -> Optional[ValidationSummary]:
        """获取最近的验证汇总"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 获取最近的验证历史
            history = self.validator.get_validation_history(
                strategy_name=strategy_name,
                start_date=start_date,
                end_date=end_date
            )

            if not history:
                return None

            # 构造汇总结果
            # TODO: 实现从历史记录构造ValidationSummary
            self.logger.info(f"获取到 {len(history)} 条验证记录")
            return None

        except Exception as e:
            self.logger.error(f"获取最近验证汇总失败: {str(e)}")
            return None

    def validate_strategy_stability(self, strategy_name: str,
                                  start_date: datetime,
                                  end_date: datetime,
                                  window_days: int = 30) -> Dict:
        """验证策略稳定性"""
        try:
            # 确保结束日期不超过当前日期
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            actual_end_date = min(end_date, today)

            # 如果开始日期就超过了当前日期，返回空结果
            if start_date > today:
                self.logger.warning(f"开始日期 {start_date.date()} 超过当前日期 {today.date()}，无法进行稳定性验证")
                return {}

            # 滑动窗口验证
            results = []
            current_start = start_date

            while current_start + timedelta(days=window_days) <= actual_end_date:
                window_end = current_start + timedelta(days=window_days)

                summary = self.validate_strategy_batch(
                    strategy_name=strategy_name,
                    start_date=current_start,
                    end_date=window_end,
                    frequency_days=7,
                    save_results=False  # 稳定性测试不保存结果
                )

                results.append({
                    'period': f"{current_start.date()} - {window_end.date()}",
                    'start_date': current_start,
                    'end_date': window_end,
                    'summary': summary
                })

                current_start += timedelta(days=7)  # 每周滑动一次

            # 如果原始结束日期超过当前日期，给出提示
            if end_date > today:
                self.logger.info(f"稳定性验证结束日期 {end_date.date()} 超过当前日期 {today.date()}，已调整为 {actual_end_date.date()}")

            # 分析稳定性
            if results:
                selection_counts = [r['summary'].avg_selection_count for r in results]
                selection_rates = [r['summary'].avg_selection_rate for r in results]
                success_rates = [r['summary'].successful_tests / r['summary'].total_tests * 100
                               if r['summary'].total_tests > 0 else 0 for r in results]

                import numpy as np

                stability_analysis = {
                    'periods': len(results),
                    'avg_selection_count': np.mean(selection_counts),
                    'selection_count_std': np.std(selection_counts),
                    'avg_selection_rate': np.mean(selection_rates),
                    'selection_rate_std': np.std(selection_rates),
                    'avg_success_rate': np.mean(success_rates),
                    'success_rate_std': np.std(success_rates),
                    'results': results
                }

                return stability_analysis

            return {}

        except Exception as e:
            self.logger.error(f"验证策略稳定性失败: {str(e)}")
            return {}

    def generate_stability_report(self, stability_analysis: Dict) -> str:
        """生成稳定性报告"""
        if not stability_analysis:
            return "没有稳定性分析数据"

        report = []
        report.append("=" * 80)
        report.append("策略稳定性分析报告")
        report.append("=" * 80)

        report.append(f"\n分析期间数: {stability_analysis['periods']}")
        report.append(f"平均选股数量: {stability_analysis['avg_selection_count']:.1f} ± {stability_analysis['selection_count_std']:.1f}")
        report.append(f"平均选股比例: {stability_analysis['avg_selection_rate']:.2f}% ± {stability_analysis['selection_rate_std']:.2f}%")
        report.append(f"平均成功率: {stability_analysis['avg_success_rate']:.1f}% ± {stability_analysis['success_rate_std']:.1f}%")

        # 稳定性评估
        report.append(f"\n稳定性评估:")
        report.append("-" * 50)

        count_cv = stability_analysis['selection_count_std'] / stability_analysis['avg_selection_count'] if stability_analysis['avg_selection_count'] > 0 else float('inf')
        rate_cv = stability_analysis['selection_rate_std'] / stability_analysis['avg_selection_rate'] if stability_analysis['avg_selection_rate'] > 0 else float('inf')

        if count_cv < 0.3 and rate_cv < 0.3:
            report.append("✓ 策略表现非常稳定")
        elif count_cv < 0.5 and rate_cv < 0.5:
            report.append("✓ 策略表现较为稳定")
        elif count_cv < 0.8 and rate_cv < 0.8:
            report.append("△ 策略表现一般稳定")
        else:
            report.append("✗ 策略表现不稳定，波动较大")

        return "\n".join(report)
