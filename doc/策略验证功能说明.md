# 策略验证功能说明

## 功能概述

策略验证功能是A股选股系统的重要组成部分，用于测试选股策略在历史数据中是否能正常工作，验证策略逻辑的正确性和实用性。

**注意：这不是传统的投资回测功能，而是策略调试和验证工具。**

## 核心特性

### 1. 多种验证模式
- **单日验证**: 测试策略在特定日期的选股表现
- **批量验证**: 测试策略在一段时间内的选股稳定性
- **指定日期验证**: 测试策略在关键时间点的表现
- **策略比较**: 比较多个策略的验证结果
- **稳定性验证**: 分析策略在不同时间窗口的稳定性

### 2. 详细的选股信息
- 选中股票列表及其技术指标
- RSI、量比等关键指标值
- 策略评分和选股原因
- 选股数量和比例统计

### 3. 验证结果记录
- 保存验证结果到数据库
- 支持历史验证记录查询
- 生成详细的验证报告

### 4. 策略调试支持
- 识别策略参数是否合理
- 检测策略条件是否过于严格
- 验证策略逻辑是否正确

## 使用方法

### 1. 命令行工具

#### 基本用法
```bash
# 单日验证
python scripts/run_validation.py technical_reversal single \
    --test-date 2024-04-07

# 批量验证
python scripts/run_validation.py technical_reversal batch \
    --start-date 2024-01-01 \
    --end-date 2024-06-30 \
    --frequency 7

# 指定日期验证
python scripts/run_validation.py technical_reversal dates \
    --test-dates "2024-04-07,2024-04-30,2024-05-15"

# 策略比较
python scripts/run_validation.py technical_reversal compare \
    --strategies "technical_reversal,volume_anomaly" \
    --start-date 2024-01-01 \
    --end-date 2024-06-30

# 稳定性验证
python scripts/run_validation.py technical_reversal stability \
    --start-date 2024-01-01 \
    --end-date 2024-12-31 \
    --window-days 30
```

#### 高级参数
```bash
python scripts/run_validation.py technical_reversal batch \
    --start-date 2024-01-01 \
    --end-date 2024-06-30 \
    --frequency 7 \
    --max-stocks 15 \
    --stock-pool "000001,000002,600000" \
    --output validation_report.txt \
    --verbose \
    --no-save
```

### 2. Python API

#### 单日验证
```python
from datetime import datetime
from src.validation.validation_manager import ValidationManager
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config

# 初始化
config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)
validation_manager = ValidationManager(data_access)

# 执行单日验证
summary = validation_manager.validate_single_date(
    strategy_name="technical_reversal",
    test_date=datetime(2024, 4, 7),
    max_stocks=15
)

# 生成报告
report = validation_manager.validator.generate_validation_report(summary)
print(report)
```

#### 批量验证
```python
# 批量验证策略
summary = validation_manager.validate_strategy_batch(
    strategy_name="technical_reversal",
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 6, 30),
    frequency_days=7
)

# 生成报告
report = validation_manager.validator.generate_validation_report(summary)
print(report)
```

#### 策略比较
```python
# 比较多个策略
results = validation_manager.compare_strategies(
    strategy_names=["technical_reversal", "volume_anomaly"],
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 6, 30)
)

# 策略比较报告
comparison = validation_manager.generate_comparison_report(results)
print(comparison)
```

## 配置参数说明

### ValidationConfig 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| strategy_name | str | - | 策略名称 |
| test_dates | List[datetime] | - | 测试日期列表 |
| max_stocks_per_test | int | 20 | 每次测试最多选股数 |
| stock_pool | List[str] | None | 股票池 |
| show_indicators | bool | True | 是否显示详细指标 |
| save_results | bool | True | 是否保存结果到数据库 |

## 验证指标说明

### 基本统计
- **测试次数**: 总验证次数
- **成功率**: 成功执行验证的比例
- **平均选股数量**: 每次验证平均选中的股票数
- **平均选股比例**: 选股数量占候选股票的比例

### 技术指标
- **RSI**: 相对强弱指数，反映股票超买超卖状态
- **量比**: 当日成交量与近期平均成交量的比值
- **价格位置**: 当前价格在近期价格区间中的位置
- **策略评分**: 策略给出的综合评分

### 验证评估
- **运行稳定性**: 基于成功率评估策略运行稳定性
- **选股能力**: 评估策略是否能选出合适数量的股票
- **参数合理性**: 评估策略条件是否过于严格或宽松

## 验证报告示例

```
================================================================================
策略验证报告: technical_reversal
================================================================================

基本信息:
策略名称: technical_reversal
测试日期数量: 9
成功测试: 9
失败测试: 0
总执行时间: 30.31 秒

统计信息:
平均选股数量: 3.7
平均选股比例: 0.73%

详细验证结果:
--------------------------------------------------------------------------------
测试日期         选股数量     选股比例       执行时间       状态
--------------------------------------------------------------------------------
2024-03-01   0        0.00     % 3.28     s 成功
2024-03-08   0        0.00     % 3.29     s 成功
2024-03-15   3        0.60     % 3.34     s 成功
2024-03-22   1        0.20     % 3.22     s 成功
2024-03-29   2        0.40     % 3.36     s 成功
2024-04-05   4        0.80     % 3.39     s 成功
2024-04-12   4        0.80     % 3.26     s 成功
2024-04-19   9        1.80     % 3.33     s 成功
2024-04-26   10       2.00     % 3.83     s 成功

最近选股详情 (最多显示3次):
================================================================================

2024-04-19 选股结果:
------------------------------------------------------------
股票代码       股票名称         评分       RSI      量比       价格位置
------------------------------------------------------------
300012     华测检测         101.5   40.3    1.62    0.14
600438     通威股份         97.8    34.8    1.22    0.02
300735     光弘科技         97.0    37.0    1.24    0.10
300568     星源材质         96.6    43.9    1.25    0.14
300136     信维通信         96.5    42.4    1.55    0.15

验证评估:
--------------------------------------------------
✓ 策略运行稳定，成功率超过90%
△ 策略选股数量较少，平均少于5只
✓ 选股比例较低，筛选较为严格
```

## 注意事项

### 1. 数据质量
- 确保历史数据完整性
- 注意停牌股票的处理
- 考虑除权除息的影响

### 2. 验证局限性
- **时点偏差**: 验证结果基于历史特定时点
- **数据偏差**: 历史数据可能存在质量问题
- **策略漂移**: 市场环境变化可能影响策略有效性

### 3. 参数设置
- 合理设置最大选股数量
- 考虑股票池的代表性
- 验证频率要符合策略特点

### 4. 结果解读
- 验证结果不代表未来表现
- 需要结合市场环境分析
- 关注策略的稳定性和适应性

## 扩展功能

### 1. 自定义指标
可以扩展 `_extract_key_indicators` 方法添加自定义指标：
- MACD指标
- 布林带位置
- 均线关系

### 2. 验证结果存储
支持将验证结果保存到数据库：
- 历史验证记录查询
- 验证结果趋势分析
- 策略表现跟踪

### 3. 高级分析
- 策略稳定性分析
- 参数敏感性测试
- 市场环境适应性分析

## 最佳实践

1. **多时间段验证**: 在不同市场环境下测试策略
2. **关键时点验证**: 重点验证重要的买入卖出时机
3. **定期验证**: 随着新数据的积累定期重新验证
4. **参数调优**: 根据验证结果调整策略参数
5. **策略比较**: 比较不同策略的验证表现

## 故障排除

### 常见问题
1. **数据不足**: 确保验证期间有足够的历史数据
2. **策略无选股**: 检查策略参数是否过于严格
3. **性能问题**: 限制股票池大小或调整验证频率
4. **连接错误**: 检查数据库连接配置

### 日志查看
验证过程中的详细日志保存在 `logs/` 目录下，可以查看具体的执行情况和错误信息。

## 使用建议

1. **策略开发阶段**: 使用单日验证和指定日期验证进行调试
2. **策略测试阶段**: 使用批量验证评估整体表现
3. **策略部署前**: 使用稳定性验证确保策略鲁棒性
4. **定期维护**: 使用策略比较监控策略相对表现
