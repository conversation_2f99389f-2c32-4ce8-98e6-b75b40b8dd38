# A股选股系统使用总结

## 系统概述

A股选股系统是一个基于技术指标的智能选股工具，支持多种选股策略，能够自动筛选符合条件的股票并提供详细的分析报告。

## 核心功能

### 1. 选股策略
- **技术反转策略** (`technical_reversal`): 基于RSI、成交量、价格位置等技术指标识别反转机会
- **成交量异常策略** (`volume_anomaly`): 识别成交量异常放大的股票

### 2. 策略验证
- 支持历史数据验证策略有效性
- 提供单日、批量、指定日期等多种验证模式
- 显示详细的技术指标和选股原因

### 3. 数据管理
- MySQL数据库存储股票基本信息和交易数据
- 支持历史选股结果查询和分析
- 自动数据更新和维护

## 快速开始

### 1. 环境准备
```bash
cd /Users/<USER>/trade/select-in-ai
source venv/bin/activate
```

### 2. 查看可用策略
```bash
python run_selection.py info
```

### 3. 执行选股
```bash
# 执行技术反转策略
python run_selection.py select --strategy technical_reversal

# 执行成交量异常策略
python run_selection.py select --strategy volume_anomaly
```

### 4. 验证策略
```bash
# 验证策略在历史数据中的表现
python scripts/run_validation.py technical_reversal single --test-date 2024-04-07
```

## 主要命令

### 选股命令
```bash
# 查看所有策略
python run_selection.py info

# 查看特定策略信息
python run_selection.py info technical_reversal

# 执行选股（保存结果）
python run_selection.py select --strategy technical_reversal

# 执行选股（显示详细信息）
python run_selection.py select --strategy technical_reversal --details

# 执行选股（不保存结果）
python run_selection.py select --strategy technical_reversal --no-save

# 查看历史选股结果
python run_selection.py history technical_reversal

# 查看指定日期的结果
python run_selection.py history technical_reversal --date 2024-04-07
```

### 验证命令
```bash
# 单日验证
python scripts/run_validation.py technical_reversal single --test-date 2024-04-07

# 批量验证
python scripts/run_validation.py technical_reversal batch --start-date 2024-01-01 --end-date 2024-06-30

# 指定日期验证
python scripts/run_validation.py technical_reversal dates --test-dates "2024-04-07,2024-04-30"

# 策略比较
python scripts/run_validation.py technical_reversal compare --strategies "technical_reversal,volume_anomaly" --start-date 2024-01-01 --end-date 2024-06-30

# 稳定性验证
python scripts/run_validation.py technical_reversal stability --start-date 2024-01-01 --end-date 2024-12-31
```

### 测试命令
```bash
# 快速功能测试
python scripts/quick_test_selection.py

# 完整功能测试
python scripts/test_selection.py
```

## 策略详解

### 技术反转策略 (technical_reversal)

#### 选股条件
- **RSI指标**: 20-50区间，识别超卖后的反弹机会
- **成交量**: 相对10日均量放大1.2-5.0倍
- **价格位置**: 在20日高低点区间的0-60%位置
- **布林带位置**: 在布林带中下轨附近
- **均线关系**: 相对MA5、MA10有一定偏离
- **前期走势**: 前5日累计收益在-15%到3%之间
- **MACD**: 柱状图在底部区域

#### 评分机制
- 基础分数：满足条件数量 × 10分
- 加分项：RSI越低、价格位置越低、成交量适度放大
- 最终评分：基础分数 + 加分项

#### 适用场景
- 短期反弹操作
- 技术面超卖后的反转机会
- 适合3-10天的持有周期

### 成交量异常策略 (volume_anomaly)

#### 选股条件
- **成交量放大**: 相对14日均量放大2倍以上
- **价格范围**: 3-100元之间
- **最小成交量**: 100万手以上
- **排除ST股票**: 自动过滤风险股票

#### 评分机制
- 基础分数：成交量放大倍数 × 10分
- 价格位置加分：当日价格相对高低点的位置
- 换手率加分：换手率 × 2分

#### 适用场景
- 捕捉突发利好或资金关注
- 短期投机机会
- 需要结合基本面分析

## 使用建议

### 1. 日常使用流程
1. **数据检查**: 确保数据库数据完整
2. **策略验证**: 定期验证策略有效性
3. **执行选股**: 在交易日收盘后执行
4. **结果分析**: 结合基本面分析选股结果
5. **风险控制**: 设置止损止盈位

### 2. 最佳实践
- **执行时机**: 交易日收盘后（下午3:30之后）
- **结果筛选**: 优先关注评分80分以上的股票
- **分散投资**: 不要集中投资单一股票
- **止损设置**: 建议设置3-5%的止损位
- **定期验证**: 每月验证策略有效性

### 3. 风险提示
- 选股结果仅供参考，不构成投资建议
- 需要结合基本面分析和市场环境
- 注意控制仓位和风险
- 及时止损止盈

## 系统维护

### 1. 数据更新
```bash
# 更新股票基本信息
python scripts/update_stock_info.py

# 更新交易数据
python scripts/update_trading_data.py
```

### 2. 日志查看
```bash
# 查看应用日志
tail -f logs/selection_app.log

# 查看错误日志
grep ERROR logs/selection_app.log
```

### 3. 数据库维护
```bash
# 备份选股结果
mysqldump -u agent -p123456 trade-ai selection_results > backup_$(date +%Y%m%d).sql

# 清理旧日志
find logs/ -name "*.log" -mtime +30 -delete
```

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查MySQL服务和连接参数
2. **策略选不出股票**: 检查策略参数和市场条件
3. **执行速度慢**: 限制股票池大小或优化查询
4. **数据不完整**: 运行数据更新脚本

### 解决方案
- 查看日志文件确定具体错误
- 使用测试脚本验证系统功能
- 使用验证功能检查策略逻辑
- 参考文档进行配置调整

## 扩展开发

### 1. 添加新策略
- 继承 `ISelectionStrategy` 接口
- 实现必要的方法
- 在 `StrategyManager` 中注册

### 2. 自定义指标
- 在策略中添加技术指标计算
- 扩展选股条件和评分机制

### 3. 结果通知
- 添加邮件或微信通知功能
- 集成第三方通知服务

## 相关文档

- [选股策略运行指南](选股策略运行指南.md)
- [策略验证功能说明](策略验证功能说明.md)
- [策略验证功能使用指南](策略验证功能使用指南.md)
- [技术反转策略设计文档](技术反转策略设计文档.md)
- [架构设计文档](架构设计文档.md)

## 联系支持

如遇到问题，请：
1. 查看相关文档
2. 运行测试脚本诊断
3. 检查日志文件
4. 验证数据库连接和数据完整性
