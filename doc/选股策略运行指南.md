# 选股策略运行指南

## 概述

本文档详细说明如何运行A股选股系统中的各种选股策略，包括技术反转策略、成交量异常策略等。

## 环境准备

### 1. 激活虚拟环境
```bash
cd /Users/<USER>/trade/select-in-ai
source venv/bin/activate
```

### 2. 确认数据库连接
确保MySQL数据库服务正常运行：
- 服务器地址：localhost
- 用户名：agent
- 密码：123456
- 数据库名：trade-ai

### 3. 检查数据完整性
建议在运行策略前确认有足够的历史交易数据。

## 运行方式

### 方式一：交互式选股应用（推荐）

#### 查看可用策略
```bash
python run_selection.py info
```

#### 查看特定策略信息
```bash
python run_selection.py info technical_reversal
```

#### 执行选股
```bash
# 执行技术反转策略
python run_selection.py select --strategy technical_reversal

# 执行成交量异常策略
python run_selection.py select --strategy volume_anomaly

# 显示详细信息
python run_selection.py select --strategy technical_reversal --details

# 不保存到数据库
python run_selection.py select --strategy technical_reversal --no-save
```

#### 查看历史结果
```bash
# 查看技术反转策略的历史结果
python run_selection.py history technical_reversal

# 查看指定日期的结果
python run_selection.py history technical_reversal --date 2024-04-07
```

#### 示例输出
```bash
# 查看策略信息
$ python run_selection.py info technical_reversal

策略名称: 基于技术指标识别处于底部区域且有反转潜力的股票，适合短期反弹操作
策略描述: 基于技术指标识别处于底部区域且有反转潜力的股票，适合短期反弹操作

默认配置:
  rsi_min: 20.0
  rsi_max: 50.0
  volume_ratio_min: 1.2
  volume_ratio_max: 5.0
  min_volume: 300000
  price_position_min: 0.0
  price_position_max: 0.6
  ...

# 执行选股
$ python run_selection.py select --strategy technical_reversal

正在执行策略: technical_reversal
请稍候...

选股结果:
==========================================
 1. 600522 - 中天科技
    评分: 99.1
    原因: 技术反转信号: RSI超卖(28.1),交易量放大(1.28倍),价格位置较低

 2. 600732 - 爱旭股份
    评分: 96.3
    原因: 技术反转信号: RSI超卖(34.6),交易量放大(1.29倍),价格位置较低

统计信息:
----------------------------------------
选中股票数量: 2
平均评分: 97.70
最高评分: 99.10
最低评分: 96.30
价格范围: 8.45 - 12.30 元

选股结果已保存到数据库
```

### 方式二：命令行直接执行

#### 执行指定策略
```bash
python -c "
from src.selection_app import SelectionApp
app = SelectionApp()
app.run_strategy('technical_reversal')
"
```

#### 执行所有可用策略
```bash
python -c "
from src.selection_app import SelectionApp
app = SelectionApp()
app.run_all_strategies()
"
```

### 方式三：Python脚本执行

创建执行脚本 `run_strategy.py`：

```python
#!/usr/bin/env python3
"""
选股策略执行脚本
"""
import sys
from datetime import datetime
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.strategies.strategy_manager import StrategyManager

def run_strategy(strategy_name):
    """执行指定策略"""
    print(f"开始执行策略: {strategy_name}")
    print("=" * 50)

    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)

    try:
        # 创建策略管理器
        strategy_manager = StrategyManager(data_access)

        # 执行策略
        results = strategy_manager.execute_strategy(
            strategy_name=strategy_name,
            save_results=True
        )

        # 显示结果
        if results:
            print(f"\n选中 {len(results)} 只股票:")
            print("=" * 60)
            for i, result in enumerate(results, 1):
                print(f"{i:2d}. {result['stock_code']} - {result.get('stock_name', '')}")
                print(f"    评分: {result['score']:.2f}")
                print(f"    原因: {result['reason']}")
                print()
        else:
            print("未选中任何股票")

    except Exception as e:
        print(f"执行策略失败: {str(e)}")

    finally:
        data_access.close_connection()

if __name__ == "__main__":
    strategy = sys.argv[1] if len(sys.argv) > 1 else 'technical_reversal'
    run_strategy(strategy)
```

使用方法：
```bash
python run_strategy.py technical_reversal
python run_strategy.py volume_anomaly
```

### 方式四：定时执行

#### 创建定时执行脚本
```bash
#!/bin/bash
# daily_selection.sh - 每日选股脚本

cd /Users/<USER>/trade/select-in-ai
source venv/bin/activate

echo "$(date): 开始每日选股"

# 执行技术反转策略
python -c "
from src.selection_app import SelectionApp
app = SelectionApp()
app.run_strategy('technical_reversal')
"

echo "$(date): 每日选股完成"
```

#### 设置定时任务
```bash
# 编辑crontab
crontab -e

# 添加定时任务（每个交易日下午3点30分执行）
30 15 * * 1-5 /path/to/daily_selection.sh >> /path/to/logs/daily_selection.log 2>&1
```

## 可用策略

### 1. 技术反转策略 (technical_reversal)

#### 策略描述
基于技术指标识别处于底部区域且有反转潜力的股票。

#### 选股条件
- RSI: 30-45区间
- 交易量放大: 1.5-2.5倍
- 价格位置: 20日高低点区间的10%-50%
- 布林带位置: 中下轨附近

#### 执行命令
```bash
python run_selection.py
# 选择 1. technical_reversal
```

### 2. 成交量异常策略 (volume_anomaly)

#### 策略描述
识别成交量出现异常放大的股票。

#### 执行命令
```bash
python run_selection.py
# 选择 2. volume_anomaly
```

## 查看历史结果

### 查看最近选股结果
```bash
python -c "
from src.selection_app import SelectionApp
app = SelectionApp()
app.show_history('technical_reversal')
"
```

### 查看指定日期结果
```bash
python -c "
from src.selection_app import SelectionApp
app = SelectionApp()
app.show_history('technical_reversal', '2024-04-07')
"
```

### 使用数据库查询
```sql
-- 查看最近的选股结果
SELECT sr.*, si.stock_name
FROM selection_results sr
LEFT JOIN stock_info si ON sr.stock_code = si.stock_code
WHERE sr.strategy_name = 'technical_reversal'
ORDER BY sr.selection_date DESC, sr.score DESC
LIMIT 20;
```

## 策略验证

在正式运行策略前，建议先进行验证：

### 验证当前策略
```bash
python scripts/run_validation.py technical_reversal single --test-date $(date +%Y-%m-%d)
```

### 验证历史表现
```bash
python scripts/run_validation.py technical_reversal batch --start-date 2024-01-01 --end-date 2024-06-30 --frequency 7
```

## 结果解读

### 选股结果字段说明
- **stock_code**: 股票代码
- **stock_name**: 股票名称
- **score**: 策略评分（0-100分）
- **reason**: 选中原因，包含关键技术指标
- **selection_date**: 选股日期

### 技术指标含义
- **RSI**: 相对强弱指数，30以下超卖，70以上超买
- **量比**: 当日成交量/近期平均成交量
- **价格位置**: 当前价格在近期价格区间中的位置（0-1）
- **布林带位置**: 价格相对布林带的位置

## 最佳实践

### 1. 执行时机
- **推荐时间**: 交易日收盘后（下午3:30之后）
- **数据完整性**: 确保当日交易数据已更新
- **避免时间**: 开盘前和交易时间内

### 2. 结果筛选
- **关注高评分股票**: 优先关注评分80分以上的股票
- **分析选股原因**: 理解技术指标的具体数值
- **结合基本面**: 策略结果需结合基本面分析

### 3. 风险控制
- **分散投资**: 不要集中投资单一股票
- **设置止损**: 建议设置3-5%的止损位
- **及时止盈**: 建议在5-8%收益时考虑止盈

### 4. 策略监控
- **定期验证**: 每月验证策略有效性
- **参数调整**: 根据市场环境调整策略参数
- **结果跟踪**: 跟踪选股结果的实际表现

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
错误: 无法连接到MySQL数据库
解决: 检查MySQL服务是否启动，确认连接参数
```

#### 2. 策略选不出股票
```
结果: 未选中任何股票
原因: 策略条件可能过于严格
解决: 使用验证功能检查策略参数
```

#### 3. 数据不足
```
错误: 股票历史数据不足
解决: 运行数据更新脚本补充历史数据
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

### 数据更新
```bash
# 更新股票基本信息
python scripts/update_stock_info.py

# 更新交易数据
python scripts/update_trading_data.py
```

## 联系支持

如遇到问题，请：
1. 查看日志文件确定具体错误
2. 检查数据库连接和数据完整性
3. 使用验证功能测试策略逻辑
4. 参考相关文档进行故障排除

## 快速开始示例

### 第一次使用
```bash
# 1. 进入项目目录并激活环境
cd /Users/<USER>/trade/select-in-ai
source venv/bin/activate

# 2. 验证策略是否正常（可选）
python scripts/run_validation.py technical_reversal single --test-date 2024-04-07

# 3. 运行选股应用
python run_selection.py

# 4. 选择策略并查看结果
```

### 日常使用
```bash
# 每日选股流程
cd /Users/<USER>/trade/select-in-ai && source venv/bin/activate
python run_selection.py
```

## 高级功能

### 1. 批量执行多个策略
```python
# batch_selection.py
from src.selection_app import SelectionApp

app = SelectionApp()
strategies = ['technical_reversal', 'volume_anomaly']

for strategy in strategies:
    print(f"\n执行策略: {strategy}")
    print("=" * 50)
    app.run_strategy(strategy)
```

### 2. 自定义选股参数
```python
# custom_selection.py
from src.strategies.strategy_manager import StrategyManager
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config

# 初始化
config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)
strategy_manager = StrategyManager(data_access)

# 获取策略并修改参数
strategy = strategy_manager.create_strategy('technical_reversal')
strategy.config['rsi_max'] = 40.0  # 调整RSI上限
strategy.config['max_results'] = 20  # 增加最大结果数

# 执行自定义策略
results = strategy.select_stocks()
```

### 3. 结果导出
```python
# export_results.py
import pandas as pd
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config

config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)

# 查询最近的选股结果
query = """
SELECT sr.*, si.stock_name, si.industry
FROM selection_results sr
LEFT JOIN stock_info si ON sr.stock_code = si.stock_code
WHERE sr.strategy_name = 'technical_reversal'
AND sr.selection_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY sr.selection_date DESC, sr.score DESC
"""

df = pd.read_sql(query, data_access.connection)
df.to_excel('selection_results.xlsx', index=False)
print("结果已导出到 selection_results.xlsx")
```

## 监控和分析

### 1. 策略表现统计
```bash
# 查看策略历史表现
python -c "
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
import pandas as pd

config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)

query = '''
SELECT
    strategy_name,
    DATE(selection_date) as date,
    COUNT(*) as stock_count,
    AVG(score) as avg_score,
    MAX(score) as max_score
FROM selection_results
WHERE selection_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY strategy_name, DATE(selection_date)
ORDER BY date DESC
'''

df = pd.read_sql(query, data_access.connection)
print(df.to_string(index=False))
"
```

### 2. 行业分布分析
```bash
# 分析选股的行业分布
python -c "
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
import pandas as pd

config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)

query = '''
SELECT
    si.industry,
    COUNT(*) as count,
    AVG(sr.score) as avg_score
FROM selection_results sr
LEFT JOIN stock_info si ON sr.stock_code = si.stock_code
WHERE sr.selection_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
AND sr.strategy_name = 'technical_reversal'
GROUP BY si.industry
ORDER BY count DESC
'''

df = pd.read_sql(query, data_access.connection)
print('最近7天选股行业分布:')
print(df.to_string(index=False))
"
```

## 性能优化

### 1. 限制股票池
```python
# 为提高执行速度，可以限制股票池
strategy.config['stock_pool'] = ['000001', '000002', '600000']  # 指定股票池
```

### 2. 并行处理
```python
# 对于大量股票的处理，可以考虑并行处理
# 注意：需要根据实际情况调整
import multiprocessing
strategy.config['parallel'] = True
strategy.config['workers'] = multiprocessing.cpu_count() // 2
```

## 安全注意事项

### 1. 数据备份
```bash
# 定期备份选股结果
mysqldump -u agent -p123456 trade-ai selection_results > backup_$(date +%Y%m%d).sql
```

### 2. 权限控制
- 确保数据库用户权限最小化
- 定期更新数据库密码
- 限制网络访问

### 3. 日志管理
```bash
# 定期清理日志文件
find logs/ -name "*.log" -mtime +30 -delete
```

## 扩展开发

### 1. 添加新策略
参考 `src/strategies/` 目录下的现有策略实现新的选股策略。

### 2. 自定义指标
在策略中添加自定义技术指标计算。

### 3. 结果通知
```python
# 添加邮件通知功能
def send_notification(results):
    if results:
        # 发送选股结果邮件
        pass
```

## 版本更新

### 检查更新
```bash
git pull origin main
pip install -r requirements.txt
```

### 数据库迁移
```bash
# 如有数据库结构变更，运行迁移脚本
python scripts/migrate_database.py
```
