#!/usr/bin/env python3
"""
测试优化后的技术反转策略
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategies.technical_reversal_strategy import TechnicalReversalStrategy

def test_strategy_config():
    """测试策略配置"""
    print("=== 测试策略配置 ===")
    
    strategy = TechnicalReversalStrategy()
    
    # 测试基本信息
    print(f"策略名称: {strategy.get_strategy_name()}")
    print(f"策略描述: {strategy.get_strategy_description()}")
    
    # 测试配置
    config = strategy.get_config()
    print(f"\n当前配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 测试配置验证
    print(f"\n配置验证结果: {strategy.validate_config(config)}")
    
    # 测试无效配置
    invalid_config = config.copy()
    invalid_config['rsi_max'] = 150  # 无效的RSI值
    print(f"无效配置验证结果: {strategy.validate_config(invalid_config)}")

def test_technical_indicators():
    """测试技术指标计算"""
    print("\n=== 测试技术指标计算 ===")
    
    strategy = TechnicalReversalStrategy()
    
    # 模拟交易数据
    trading_data = []
    base_price = 10.0
    base_volume = 1000000
    
    for i in range(30):  # 30天数据
        price_change = (i % 5 - 2) * 0.1  # 模拟价格波动
        volume_change = 1.0 + (i % 3 - 1) * 0.2  # 模拟成交量变化
        
        price = base_price + price_change
        volume = int(base_volume * volume_change)
        
        trading_data.append({
            'trade_date': f'2024-01-{i+1:02d}',
            'open_price': price,
            'high_price': price * 1.02,
            'low_price': price * 0.98,
            'close_price': price,
            'volume': volume,
            'turnover_rate': 2.5
        })
    
    # 计算技术指标
    indicators = strategy._calculate_technical_indicators(trading_data)
    
    # 显示最后几天的指标
    print("最后5天的技术指标:")
    for i in range(-5, 0):
        data = indicators[i]
        print(f"日期: {data['trade_date']}")
        print(f"  收盘价: {data['close_price']:.2f}")
        print(f"  RSI: {data['rsi']:.2f}")
        print(f"  成交量比率: {data['volume_ratio_5']:.2f}")
        print(f"  布林带下轨: {data['bb_lower']:.2f}")
        print(f"  布林带位置: {data['bb_position']:.2f}")
        print()

def test_condition_checks():
    """测试条件检查方法"""
    print("=== 测试条件检查方法 ===")
    
    strategy = TechnicalReversalStrategy()
    
    # 创建测试数据 - 模拟突破布林带下轨的情况
    test_data = []
    for i in range(25):
        if i < 20:
            price = 10.0 + (i % 3 - 1) * 0.1  # 正常波动
            volume = 1000000
        else:
            price = 9.5 - (i - 20) * 0.1  # 价格下跌突破下轨
            volume = 1500000 + (i - 20) * 200000  # 成交量放大
        
        test_data.append({
            'trade_date': f'2024-01-{i+1:02d}',
            'open_price': price,
            'high_price': price * 1.01,
            'low_price': price * 0.99,
            'close_price': price,
            'volume': volume,
            'turnover_rate': 2.5
        })
    
    # 计算指标
    indicators = strategy._calculate_technical_indicators(test_data)
    
    # 测试布林带突破检查
    bb_breakthrough = strategy._check_bollinger_breakthrough(indicators)
    print(f"布林带突破检查: {bb_breakthrough}")
    
    # 测试成交量放大检查
    volume_amplified = strategy._check_volume_amplification(indicators)
    print(f"成交量放大检查: {volume_amplified}")
    
    # 显示最新数据
    latest = indicators[-1]
    print(f"\n最新数据:")
    print(f"  收盘价: {latest['close_price']:.2f}")
    print(f"  RSI: {latest['rsi']:.2f}")
    print(f"  布林带下轨: {latest['bb_lower']:.2f}")
    print(f"  成交量比率: {latest['volume_ratio_5']:.2f}")

if __name__ == "__main__":
    print("技术反转策略优化测试")
    print("=" * 50)
    
    try:
        test_strategy_config()
        test_technical_indicators()
        test_condition_checks()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
