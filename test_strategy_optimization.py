#!/usr/bin/env python3
"""
测试优化后的技术反转策略
验证新的逻辑：成交量和布林带必须满足，RSI参与评分
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from datetime import datetime, timedelta
from src.strategies.technical_reversal_strategy import TechnicalReversalStrategy
from src.data.access.mysql_access import MySQLDataAccess

def test_strategy_logic():
    """测试策略逻辑"""
    print("=== 测试优化后的技术反转策略 ===")

    # 初始化策略
    strategy = TechnicalReversalStrategy()

    print(f"策略名称: {strategy.get_strategy_name()}")
    print(f"策略描述: {strategy.get_strategy_description()}")
    print()

    # 显示策略配置
    config = strategy.get_config()
    print("策略配置:")
    print(f"  RSI最大值: {config['rsi_max']}")
    print(f"  成交量放大倍数: {config['volume_ratio_min']}-{config['volume_ratio_max']}")
    print(f"  布林带突破阈值: {config['bb_breakthrough_threshold']}")
    print()

    # 初始化数据访问
    try:
        data_access = MySQLDataAccess()
        print("数据库连接成功")

        # 测试选股
        print("\n开始测试选股...")
        target_date = datetime(2024, 12, 20)  # 使用一个固定日期进行测试

        results = strategy.execute(data_access, target_date)

        print(f"\n选股结果: 共选中 {len(results)} 只股票")

        if results:
            print("\n详细结果:")
            print("-" * 100)
            print(f"{'股票代码':<10} {'股票名称':<15} {'评分':<8} {'RSI':<8} {'量比':<8} {'选股原因'}")
            print("-" * 100)

            for result in results[:10]:  # 只显示前10个结果
                print(f"{result['stock_code']:<10} {result['stock_name']:<15} "
                      f"{result['score']:<8.2f} {result['rsi']:<8.1f} "
                      f"{result['volume_ratio']:<8.2f} {result['reason']}")
        else:
            print("未找到符合条件的股票")
            print("\n这可能是因为:")
            print("1. 布林带下轨突破条件过于严格")
            print("2. 成交量放大条件在当前市场环境下较少出现")
            print("3. 目标日期的市场数据不足")

        # data_access.close()  # MySQL连接会自动管理

    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_scoring_logic():
    """测试评分逻辑"""
    print("\n=== 测试评分逻辑 ===")

    strategy = TechnicalReversalStrategy()

    # 模拟不同RSI值的评分情况
    test_cases = [
        {"rsi": 15, "description": "极度超卖"},
        {"rsi": 25, "description": "超卖"},
        {"rsi": 30, "description": "临界超卖"},
        {"rsi": 35, "description": "RSI上限"},
        {"rsi": 40, "description": "偏高但可接受"},
        {"rsi": 50, "description": "中性"},
        {"rsi": 60, "description": "偏高"}
    ]

    print("RSI评分测试:")
    print(f"{'RSI值':<8} {'描述':<15} {'基础分数':<10} {'额外加分':<10} {'总分估算'}")
    print("-" * 60)

    for case in test_cases:
        rsi = case["rsi"]
        desc = case["description"]

        # 计算基础分数（模拟策略逻辑）
        if rsi < strategy.config['rsi_max']:
            base_score = 20
        else:
            if rsi <= 50:
                base_score = max(0, (50 - rsi) / (50 - strategy.config['rsi_max']) * 10)
            else:
                base_score = 0

        # 计算额外加分
        bonus_score = 0
        if rsi < strategy.config['rsi_max']:
            if rsi < 20:
                bonus_score = (20 - rsi) * 2
            elif rsi < 25:
                bonus_score = (25 - rsi) * 1.5
            elif rsi < 30:
                bonus_score = (30 - rsi) * 1

        # 加上必要条件的分数（布林带40分 + 成交量40分）
        total_estimated = 80 + base_score + bonus_score

        print(f"{rsi:<8} {desc:<15} {base_score:<10.1f} {bonus_score:<10.1f} {total_estimated:<10.1f}")

if __name__ == "__main__":
    test_scoring_logic()
    test_strategy_logic()
